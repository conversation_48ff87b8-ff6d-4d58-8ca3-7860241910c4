# 🎯 雷电模拟器中控系统 - Git忽略文件

# 虚拟环境
venv/
env/
ENV/
.venv/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 打包输出
build/
dist/
*.egg-info/
*.spec

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 数据库文件
*.db
*.db-shm
*.db-wal
*.sqlite
*.sqlite3

# 配置文件（可能包含敏感信息）
auth_config.json

# 临时文件
temp/
tmp/
*.tmp

# 截图和测试文件
screenshots/
test_screenshots/
test_results/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 用户数据
sent_users.txt
guanzhu.txt

# 调试文件
debug_*.xml
element_reports/

# PyInstaller临时文件
*.manifest
*.spec
