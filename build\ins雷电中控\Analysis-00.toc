(['E:\\VScodexiangmu\\xunileidian2\\main.py'],
 ['E:\\VScodexiangmu\\xunileidian2'],
 ['PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'requests', 'psutil'],
 [('E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('app_config.json',
   'E:\\VScodexiangmu\\xunileidian2\\app_config.json',
   'DATA'),
  ('img\\V2.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\ins.png', 'DATA')],
 '3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'E:\\VScodexiangmu\\xunileidian2\\main.py', 'PYSOURCE')],
 [('subprocess', 'E:\\python-3.12.9\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'E:\\python-3.12.9\\Lib\\signal.py', 'PYMODULE'),
  ('selectors', 'E:\\python-3.12.9\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'E:\\python-3.12.9\\Lib\\contextlib.py', 'PYMODULE'),
  ('_strptime', 'E:\\python-3.12.9\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'E:\\python-3.12.9\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'E:\\python-3.12.9\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('calendar', 'E:\\python-3.12.9\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'E:\\python-3.12.9\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'E:\\python-3.12.9\\Lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'E:\\python-3.12.9\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'E:\\python-3.12.9\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python-3.12.9\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python-3.12.9\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'E:\\python-3.12.9\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'E:\\python-3.12.9\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'E:\\python-3.12.9\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'E:\\python-3.12.9\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'E:\\python-3.12.9\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'E:\\python-3.12.9\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'E:\\python-3.12.9\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python-3.12.9\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'E:\\python-3.12.9\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'E:\\python-3.12.9\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'E:\\python-3.12.9\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'E:\\python-3.12.9\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'E:\\python-3.12.9\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc', 'E:\\python-3.12.9\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'E:\\python-3.12.9\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'E:\\python-3.12.9\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python-3.12.9\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'E:\\python-3.12.9\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'E:\\python-3.12.9\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python-3.12.9\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python-3.12.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'E:\\python-3.12.9\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'E:\\python-3.12.9\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'E:\\python-3.12.9\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'E:\\python-3.12.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'E:\\python-3.12.9\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'E:\\python-3.12.9\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'E:\\python-3.12.9\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'E:\\python-3.12.9\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime',
   'E:\\python-3.12.9\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'E:\\python-3.12.9\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'E:\\python-3.12.9\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'E:\\python-3.12.9\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'E:\\python-3.12.9\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'E:\\python-3.12.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse', 'E:\\python-3.12.9\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python-3.12.9\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'E:\\python-3.12.9\\Lib\\socket.py', 'PYMODULE'),
  ('quopri', 'E:\\python-3.12.9\\Lib\\quopri.py', 'PYMODULE'),
  ('inspect', 'E:\\python-3.12.9\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'E:\\python-3.12.9\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'E:\\python-3.12.9\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'E:\\python-3.12.9\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'E:\\python-3.12.9\\Lib\\ast.py', 'PYMODULE'),
  ('email', 'E:\\python-3.12.9\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'E:\\python-3.12.9\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'E:\\python-3.12.9\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'E:\\python-3.12.9\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'E:\\python-3.12.9\\Lib\\tokenize.py', 'PYMODULE'),
  ('struct', 'E:\\python-3.12.9\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.util', 'E:\\python-3.12.9\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'E:\\python-3.12.9\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'E:\\python-3.12.9\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'E:\\python-3.12.9\\Lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'E:\\python-3.12.9\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'E:\\python-3.12.9\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python-3.12.9\\Lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'E:\\python-3.12.9\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'E:\\python-3.12.9\\Lib\\gettext.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'E:\\python-3.12.9\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'E:\\python-3.12.9\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'E:\\python-3.12.9\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request', 'E:\\python-3.12.9\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'E:\\python-3.12.9\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python-3.12.9\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'E:\\python-3.12.9\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'E:\\python-3.12.9\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python-3.12.9\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python-3.12.9\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'E:\\python-3.12.9\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'E:\\python-3.12.9\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'E:\\python-3.12.9\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'E:\\python-3.12.9\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'E:\\python-3.12.9\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'E:\\python-3.12.9\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'E:\\python-3.12.9\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'E:\\python-3.12.9\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python-3.12.9\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'E:\\python-3.12.9\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'E:\\python-3.12.9\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'E:\\python-3.12.9\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'E:\\python-3.12.9\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'E:\\python-3.12.9\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python-3.12.9\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'E:\\python-3.12.9\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'E:\\python-3.12.9\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'E:\\python-3.12.9\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'E:\\python-3.12.9\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'E:\\python-3.12.9\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python-3.12.9\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'E:\\python-3.12.9\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'E:\\python-3.12.9\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'E:\\python-3.12.9\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'E:\\python-3.12.9\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite', 'E:\\python-3.12.9\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'E:\\python-3.12.9\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python-3.12.9\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'E:\\python-3.12.9\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'E:\\python-3.12.9\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util', 'E:\\python-3.12.9\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'E:\\python-3.12.9\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'E:\\python-3.12.9\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'E:\\python-3.12.9\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'E:\\python-3.12.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'E:\\python-3.12.9\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues', 'E:\\python-3.12.9\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners',
   'E:\\python-3.12.9\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'E:\\python-3.12.9\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'E:\\python-3.12.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'E:\\python-3.12.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks', 'E:\\python-3.12.9\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'E:\\python-3.12.9\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python-3.12.9\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto',
   'E:\\python-3.12.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'E:\\python-3.12.9\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'E:\\python-3.12.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'E:\\python-3.12.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'E:\\python-3.12.9\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python-3.12.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'E:\\python-3.12.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'E:\\python-3.12.9\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'E:\\python-3.12.9\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python-3.12.9\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'E:\\python-3.12.9\\Lib\\platform.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'E:\\python-3.12.9\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'E:\\python-3.12.9\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'E:\\python-3.12.9\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'E:\\python-3.12.9\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'E:\\python-3.12.9\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'E:\\python-3.12.9\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'E:\\python-3.12.9\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'E:\\python-3.12.9\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'E:\\python-3.12.9\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python-3.12.9\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'E:\\python-3.12.9\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'E:\\python-3.12.9\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'E:\\python-3.12.9\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'E:\\python-3.12.9\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'E:\\python-3.12.9\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types', 'E:\\python-3.12.9\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'E:\\python-3.12.9\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'E:\\python-3.12.9\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'E:\\python-3.12.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__', 'E:\\python-3.12.9\\Lib\\__future__.py', 'PYMODULE'),
  ('psutil',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('requests',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'E:\\python-3.12.9\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('PyQt6',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'E:\\python-3.12.9\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'E:\\python-3.12.9\\Lib\\stringprep.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'E:\\python-3.12.9\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('threading', 'E:\\python-3.12.9\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'E:\\python-3.12.9\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('core.authsdk',
   'E:\\VScodexiangmu\\xunileidian2\\core\\authsdk.py',
   'PYMODULE'),
  ('core', '-', 'PYMODULE'),
  ('json', 'E:\\python-3.12.9\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python-3.12.9\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python-3.12.9\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python-3.12.9\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('ui.main_window_v2',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\main_window_v2.py',
   'PYMODULE'),
  ('ui', 'E:\\VScodexiangmu\\xunileidian2\\ui\\__init__.py', 'PYMODULE'),
  ('ui.instagram_follow_ui',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\instagram_follow_ui.py',
   'PYMODULE'),
  ('ui.style_manager',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\style_manager.py',
   'PYMODULE'),
  ('ui.instagram_dm_ui',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\instagram_dm_ui.py',
   'PYMODULE'),
  ('ui.basic_config_ui',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\basic_config_ui.py',
   'PYMODULE'),
  ('ui.settings_ui',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\settings_ui.py',
   'PYMODULE'),
  ('core.heartbeat_manager',
   'E:\\VScodexiangmu\\xunileidian2\\core\\heartbeat_manager.py',
   'PYMODULE'),
  ('core.screenshot_manager',
   'E:\\VScodexiangmu\\xunileidian2\\core\\screenshot_manager.py',
   'PYMODULE'),
  ('core.native',
   'E:\\VScodexiangmu\\xunileidian2\\core\\native\\__init__.py',
   'PYMODULE'),
  ('core.native.screenshot_engine',
   'E:\\VScodexiangmu\\xunileidian2\\core\\native\\screenshot_engine.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'E:\\python-3.12.9\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.Image',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'E:\\python-3.12.9\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('core.native.base_api',
   'E:\\VScodexiangmu\\xunileidian2\\core\\native\\base_api.py',
   'PYMODULE'),
  ('core.native.image_recognition_engine',
   'E:\\VScodexiangmu\\xunileidian2\\core\\native\\image_recognition_engine.py',
   'PYMODULE'),
  ('core.unified_emulator_manager',
   'E:\\VScodexiangmu\\xunileidian2\\core\\unified_emulator_manager.py',
   'PYMODULE'),
  ('data.models.emulator_model',
   'E:\\VScodexiangmu\\xunileidian2\\data\\models\\emulator_model.py',
   'PYMODULE'),
  ('data.models',
   'E:\\VScodexiangmu\\xunileidian2\\data\\models\\__init__.py',
   'PYMODULE'),
  ('data', 'E:\\VScodexiangmu\\xunileidian2\\data\\__init__.py', 'PYMODULE'),
  ('sqlite3', 'E:\\python-3.12.9\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'E:\\python-3.12.9\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__',
   'E:\\python-3.12.9\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('code', 'E:\\python-3.12.9\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python-3.12.9\\Lib\\codeop.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'E:\\python-3.12.9\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('data.repositories.emulator_repository',
   'E:\\VScodexiangmu\\xunileidian2\\data\\repositories\\emulator_repository.py',
   'PYMODULE'),
  ('data.repositories',
   'E:\\VScodexiangmu\\xunileidian2\\data\\repositories\\__init__.py',
   'PYMODULE'),
  ('data.database_manager',
   'E:\\VScodexiangmu\\xunileidian2\\data\\database_manager.py',
   'PYMODULE'),
  ('core.status_converter',
   'E:\\VScodexiangmu\\xunileidian2\\core\\status_converter.py',
   'PYMODULE'),
  ('ui.styled_widgets',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\styled_widgets.py',
   'PYMODULE'),
  ('ui.ui_service_layer',
   'E:\\VScodexiangmu\\xunileidian2\\ui\\ui_service_layer.py',
   'PYMODULE'),
  ('core.async_bridge',
   'E:\\VScodexiangmu\\xunileidian2\\core\\async_bridge.py',
   'PYMODULE'),
  ('core.instagram_follow_task',
   'E:\\VScodexiangmu\\xunileidian2\\core\\instagram_follow_task.py',
   'PYMODULE'),
  ('core.instagram_task',
   'E:\\VScodexiangmu\\xunileidian2\\core\\instagram_task.py',
   'PYMODULE'),
  ('core.leidianapi.雷电一键找图',
   'E:\\VScodexiangmu\\xunileidian2\\core\\leidianapi\\雷电一键找图.py',
   'PYMODULE'),
  ('core.leidianapi', '-', 'PYMODULE'),
  ('core.leidianapi.LeiDian_Reorganized',
   'E:\\VScodexiangmu\\xunileidian2\\core\\leidianapi\\LeiDian_Reorganized.py',
   'PYMODULE'),
  ('core.config_hot_reload',
   'E:\\VScodexiangmu\\xunileidian2\\core\\config_hot_reload.py',
   'PYMODULE'),
  ('core.window_arrangement_manager',
   'E:\\VScodexiangmu\\xunileidian2\\core\\window_arrangement_manager.py',
   'PYMODULE'),
  ('core.simple_config',
   'E:\\VScodexiangmu\\xunileidian2\\core\\simple_config.py',
   'PYMODULE'),
  ('logging', 'E:\\python-3.12.9\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('core.logger_manager',
   'E:\\VScodexiangmu\\xunileidian2\\core\\logger_manager.py',
   'PYMODULE'),
  ('pathlib', 'E:\\python-3.12.9\\Lib\\pathlib.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python-3.12.9\\Lib\\tracemalloc.py', 'PYMODULE')],
 [('python312.dll', 'E:\\python-3.12.9\\python312.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd', 'E:\\python-3.12.9\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'E:\\python-3.12.9\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python-3.12.9\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\python-3.12.9\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python-3.12.9\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\python-3.12.9\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python-3.12.9\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\python-3.12.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'E:\\python-3.12.9\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python-3.12.9\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\python-3.12.9\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\python-3.12.9\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'E:\\python-3.12.9\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\python-3.12.9\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'E:\\python-3.12.9\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_avif.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'E:\\python-3.12.9\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'E:\\python-3.12.9\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'E:\\python-3.12.9\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'E:\\python-3.12.9\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'E:\\python-3.12.9\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'E:\\python-3.12.9\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\python-3.12.9\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'E:\\python-3.12.9\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('sqlite3.dll', 'E:\\python-3.12.9\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY')],
 [],
 [],
 [('app_config.json',
   'E:\\VScodexiangmu\\xunileidian2\\app_config.json',
   'DATA'),
  ('img\\V2.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\ins.png', 'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\base_library.zip',
   'DATA')],
 [('sre_constants', 'E:\\python-3.12.9\\Lib\\sre_constants.py', 'PYMODULE'),
  ('reprlib', 'E:\\python-3.12.9\\Lib\\reprlib.py', 'PYMODULE'),
  ('enum', 'E:\\python-3.12.9\\Lib\\enum.py', 'PYMODULE'),
  ('weakref', 'E:\\python-3.12.9\\Lib\\weakref.py', 'PYMODULE'),
  ('collections.abc',
   'E:\\python-3.12.9\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'E:\\python-3.12.9\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types', 'E:\\python-3.12.9\\Lib\\types.py', 'PYMODULE'),
  ('posixpath', 'E:\\python-3.12.9\\Lib\\posixpath.py', 'PYMODULE'),
  ('functools', 'E:\\python-3.12.9\\Lib\\functools.py', 'PYMODULE'),
  ('ntpath', 'E:\\python-3.12.9\\Lib\\ntpath.py', 'PYMODULE'),
  ('linecache', 'E:\\python-3.12.9\\Lib\\linecache.py', 'PYMODULE'),
  ('genericpath', 'E:\\python-3.12.9\\Lib\\genericpath.py', 'PYMODULE'),
  ('sre_parse', 'E:\\python-3.12.9\\Lib\\sre_parse.py', 'PYMODULE'),
  ('keyword', 'E:\\python-3.12.9\\Lib\\keyword.py', 'PYMODULE'),
  ('locale', 'E:\\python-3.12.9\\Lib\\locale.py', 'PYMODULE'),
  ('abc', 'E:\\python-3.12.9\\Lib\\abc.py', 'PYMODULE'),
  ('_weakrefset', 'E:\\python-3.12.9\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('operator', 'E:\\python-3.12.9\\Lib\\operator.py', 'PYMODULE'),
  ('io', 'E:\\python-3.12.9\\Lib\\io.py', 'PYMODULE'),
  ('sre_compile', 'E:\\python-3.12.9\\Lib\\sre_compile.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'E:\\python-3.12.9\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'E:\\python-3.12.9\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'E:\\python-3.12.9\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'E:\\python-3.12.9\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'E:\\python-3.12.9\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'E:\\python-3.12.9\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'E:\\python-3.12.9\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'E:\\python-3.12.9\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'E:\\python-3.12.9\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'E:\\python-3.12.9\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'E:\\python-3.12.9\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'E:\\python-3.12.9\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'E:\\python-3.12.9\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'E:\\python-3.12.9\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'E:\\python-3.12.9\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'E:\\python-3.12.9\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'E:\\python-3.12.9\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'E:\\python-3.12.9\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'E:\\python-3.12.9\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'E:\\python-3.12.9\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'E:\\python-3.12.9\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'E:\\python-3.12.9\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'E:\\python-3.12.9\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'E:\\python-3.12.9\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'E:\\python-3.12.9\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'E:\\python-3.12.9\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'E:\\python-3.12.9\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'E:\\python-3.12.9\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'E:\\python-3.12.9\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'E:\\python-3.12.9\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'E:\\python-3.12.9\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'E:\\python-3.12.9\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'E:\\python-3.12.9\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'E:\\python-3.12.9\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'E:\\python-3.12.9\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'E:\\python-3.12.9\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'E:\\python-3.12.9\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'E:\\python-3.12.9\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'E:\\python-3.12.9\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'E:\\python-3.12.9\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'E:\\python-3.12.9\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'E:\\python-3.12.9\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'E:\\python-3.12.9\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'E:\\python-3.12.9\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'E:\\python-3.12.9\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'E:\\python-3.12.9\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'E:\\python-3.12.9\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'E:\\python-3.12.9\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'E:\\python-3.12.9\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'E:\\python-3.12.9\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('copyreg', 'E:\\python-3.12.9\\Lib\\copyreg.py', 'PYMODULE'),
  ('codecs', 'E:\\python-3.12.9\\Lib\\codecs.py', 'PYMODULE'),
  ('heapq', 'E:\\python-3.12.9\\Lib\\heapq.py', 'PYMODULE'),
  ('_collections_abc',
   'E:\\python-3.12.9\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('re._parser', 'E:\\python-3.12.9\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'E:\\python-3.12.9\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'E:\\python-3.12.9\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'E:\\python-3.12.9\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'E:\\python-3.12.9\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('stat', 'E:\\python-3.12.9\\Lib\\stat.py', 'PYMODULE'),
  ('traceback', 'E:\\python-3.12.9\\Lib\\traceback.py', 'PYMODULE'),
  ('os', 'E:\\python-3.12.9\\Lib\\os.py', 'PYMODULE'),
  ('warnings', 'E:\\python-3.12.9\\Lib\\warnings.py', 'PYMODULE')])
