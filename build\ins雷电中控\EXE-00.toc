('E:\\VScodexiangmu\\xunileidian2\\dist\\ins雷电中控.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON>e,
 False,
 'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON>als<PERSON>,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\ins雷电中控.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'E:\\VScodexiangmu\\xunileidian2\\main.py', 'PYSOURCE'),
  ('python312.dll', 'E:\\python-3.12.9\\python312.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('select.pyd', 'E:\\python-3.12.9\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'E:\\python-3.12.9\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python-3.12.9\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\python-3.12.9\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python-3.12.9\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\python-3.12.9\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python-3.12.9\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\python-3.12.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'E:\\python-3.12.9\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python-3.12.9\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\python-3.12.9\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\python-3.12.9\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'E:\\python-3.12.9\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\python-3.12.9\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'E:\\python-3.12.9\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_avif.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'E:\\python-3.12.9\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'E:\\python-3.12.9\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'E:\\python-3.12.9\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'E:\\python-3.12.9\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'E:\\python-3.12.9\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'E:\\python-3.12.9\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\python-3.12.9\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'E:\\python-3.12.9\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('sqlite3.dll', 'E:\\python-3.12.9\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('app_config.json',
   'E:\\VScodexiangmu\\xunileidian2\\app_config.json',
   'DATA'),
  ('img\\V2.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'E:\\VScodexiangmu\\xunileidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'E:\\VScodexiangmu\\xunileidian2\\img\\ins.png', 'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\py.typed',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1753848360,
 [('runw.exe',
   'E:\\VScodexiangmu\\xunileidian2\\venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'E:\\python-3.12.9\\python312.dll')
