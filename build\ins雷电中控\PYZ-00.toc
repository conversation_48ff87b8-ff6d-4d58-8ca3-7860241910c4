('e:\\VScodexiangmu\\xunileidian2\\build\\ins雷电中控\\PYZ-00.pyz',
 [('Crypto',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC2',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\ARC2.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\DES.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES3',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\DES3.py',
   'PYMODULE'),
  ('Crypto.Cipher.PKCS1_v1_5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kw',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kw.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_kwp',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_kwp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Cipher._pkcs1_oaep_decode',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_oaep_decode.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.IO',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\__init__.py',
   'PYMODULE'),
  ('Crypto.IO.PEM',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\PEM.py',
   'PYMODULE'),
  ('Crypto.IO.PKCS8',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\PKCS8.py',
   'PYMODULE'),
  ('Crypto.IO._PBES',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\IO\\_PBES.py',
   'PYMODULE'),
  ('Crypto.Math',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Numbers',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\Numbers.py',
   'PYMODULE'),
  ('Crypto.Math.Primality',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\Primality.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerBase',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerBase.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerCustom',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerCustom.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerGMP',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerGMP.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerNative',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Math\\_IntegerNative.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.PublicKey',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\__init__.py',
   'PYMODULE'),
  ('Crypto.PublicKey.RSA',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\RSA.py',
   'PYMODULE'),
  ('Crypto.PublicKey._openssh',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\PublicKey\\_openssh.py',
   'PYMODULE'),
  ('Crypto.Random',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto.Random.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Random\\random.py',
   'PYMODULE'),
  ('Crypto.Signature',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\__init__.py',
   'PYMODULE'),
  ('Crypto.Signature.PKCS1_v1_5',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Signature.pkcs1_15',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Signature\\pkcs1_15.py',
   'PYMODULE'),
  ('Crypto.Util',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util.asn1',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\asn1.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'E:\\python-3.12.9\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('PIL',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('__future__', 'E:\\python-3.12.9\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'E:\\python-3.12.9\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'E:\\python-3.12.9\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'E:\\python-3.12.9\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'E:\\python-3.12.9\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\python-3.12.9\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'E:\\python-3.12.9\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'E:\\python-3.12.9\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'E:\\python-3.12.9\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'E:\\python-3.12.9\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'E:\\python-3.12.9\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'E:\\python-3.12.9\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiofiles',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE'),
  ('aiofiles.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\base.py',
   'PYMODULE'),
  ('aiofiles.tempfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile.temptypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE'),
  ('aiofiles.threadpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE'),
  ('aiofiles.threadpool.binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE'),
  ('aiofiles.threadpool.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE'),
  ('aiofiles.threadpool.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE'),
  ('argparse', 'E:\\python-3.12.9\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'E:\\python-3.12.9\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'E:\\python-3.12.9\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'E:\\python-3.12.9\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'E:\\python-3.12.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'E:\\python-3.12.9\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'E:\\python-3.12.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'E:\\python-3.12.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'E:\\python-3.12.9\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'E:\\python-3.12.9\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'E:\\python-3.12.9\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'E:\\python-3.12.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'E:\\python-3.12.9\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners',
   'E:\\python-3.12.9\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'E:\\python-3.12.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'E:\\python-3.12.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'E:\\python-3.12.9\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'E:\\python-3.12.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'E:\\python-3.12.9\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'E:\\python-3.12.9\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'E:\\python-3.12.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'E:\\python-3.12.9\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'E:\\python-3.12.9\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'E:\\python-3.12.9\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'E:\\python-3.12.9\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'E:\\python-3.12.9\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'E:\\python-3.12.9\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'E:\\python-3.12.9\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'E:\\python-3.12.9\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'E:\\python-3.12.9\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'E:\\python-3.12.9\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'E:\\python-3.12.9\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'E:\\python-3.12.9\\Lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent', 'E:\\python-3.12.9\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'E:\\python-3.12.9\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'E:\\python-3.12.9\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'E:\\python-3.12.9\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'E:\\python-3.12.9\\Lib\\copy.py', 'PYMODULE'),
  ('core', '-', 'PYMODULE'),
  ('core.async_bridge',
   'e:\\VScodexiangmu\\xunileidian2\\core\\async_bridge.py',
   'PYMODULE'),
  ('core.authsdk',
   'e:\\VScodexiangmu\\xunileidian2\\core\\authsdk.py',
   'PYMODULE'),
  ('core.config_hot_reload',
   'e:\\VScodexiangmu\\xunileidian2\\core\\config_hot_reload.py',
   'PYMODULE'),
  ('core.heartbeat_manager',
   'e:\\VScodexiangmu\\xunileidian2\\core\\heartbeat_manager.py',
   'PYMODULE'),
  ('core.instagram_follow_task',
   'e:\\VScodexiangmu\\xunileidian2\\core\\instagram_follow_task.py',
   'PYMODULE'),
  ('core.instagram_task',
   'e:\\VScodexiangmu\\xunileidian2\\core\\instagram_task.py',
   'PYMODULE'),
  ('core.leidianapi', '-', 'PYMODULE'),
  ('core.leidianapi.LeiDian_Reorganized',
   'e:\\VScodexiangmu\\xunileidian2\\core\\leidianapi\\LeiDian_Reorganized.py',
   'PYMODULE'),
  ('core.leidianapi.雷电一键找图',
   'e:\\VScodexiangmu\\xunileidian2\\core\\leidianapi\\雷电一键找图.py',
   'PYMODULE'),
  ('core.logger_manager',
   'e:\\VScodexiangmu\\xunileidian2\\core\\logger_manager.py',
   'PYMODULE'),
  ('core.native',
   'e:\\VScodexiangmu\\xunileidian2\\core\\native\\__init__.py',
   'PYMODULE'),
  ('core.native.base_api',
   'e:\\VScodexiangmu\\xunileidian2\\core\\native\\base_api.py',
   'PYMODULE'),
  ('core.native.image_recognition_engine',
   'e:\\VScodexiangmu\\xunileidian2\\core\\native\\image_recognition_engine.py',
   'PYMODULE'),
  ('core.native.screenshot_engine',
   'e:\\VScodexiangmu\\xunileidian2\\core\\native\\screenshot_engine.py',
   'PYMODULE'),
  ('core.screenshot_manager',
   'e:\\VScodexiangmu\\xunileidian2\\core\\screenshot_manager.py',
   'PYMODULE'),
  ('core.simple_config',
   'e:\\VScodexiangmu\\xunileidian2\\core\\simple_config.py',
   'PYMODULE'),
  ('core.status_converter',
   'e:\\VScodexiangmu\\xunileidian2\\core\\status_converter.py',
   'PYMODULE'),
  ('core.unified_emulator_manager',
   'e:\\VScodexiangmu\\xunileidian2\\core\\unified_emulator_manager.py',
   'PYMODULE'),
  ('core.window_arrangement_manager',
   'e:\\VScodexiangmu\\xunileidian2\\core\\window_arrangement_manager.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'E:\\python-3.12.9\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'E:\\python-3.12.9\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'E:\\python-3.12.9\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'E:\\python-3.12.9\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'E:\\python-3.12.9\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'E:\\python-3.12.9\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'E:\\python-3.12.9\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'E:\\python-3.12.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('data', 'e:\\VScodexiangmu\\xunileidian2\\data\\__init__.py', 'PYMODULE'),
  ('data.database_manager',
   'e:\\VScodexiangmu\\xunileidian2\\data\\database_manager.py',
   'PYMODULE'),
  ('data.models',
   'e:\\VScodexiangmu\\xunileidian2\\data\\models\\__init__.py',
   'PYMODULE'),
  ('data.models.emulator_model',
   'e:\\VScodexiangmu\\xunileidian2\\data\\models\\emulator_model.py',
   'PYMODULE'),
  ('data.repositories',
   'e:\\VScodexiangmu\\xunileidian2\\data\\repositories\\__init__.py',
   'PYMODULE'),
  ('data.repositories.emulator_repository',
   'e:\\VScodexiangmu\\xunileidian2\\data\\repositories\\emulator_repository.py',
   'PYMODULE'),
  ('dataclasses', 'E:\\python-3.12.9\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'E:\\python-3.12.9\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'E:\\python-3.12.9\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'E:\\python-3.12.9\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'E:\\python-3.12.9\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'E:\\python-3.12.9\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'E:\\python-3.12.9\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'E:\\python-3.12.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\python-3.12.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'E:\\python-3.12.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'E:\\python-3.12.9\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'E:\\python-3.12.9\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'E:\\python-3.12.9\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'E:\\python-3.12.9\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'E:\\python-3.12.9\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'E:\\python-3.12.9\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'E:\\python-3.12.9\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'E:\\python-3.12.9\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'E:\\python-3.12.9\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'E:\\python-3.12.9\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'E:\\python-3.12.9\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'E:\\python-3.12.9\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'E:\\python-3.12.9\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'E:\\python-3.12.9\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'E:\\python-3.12.9\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'E:\\python-3.12.9\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'E:\\python-3.12.9\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'E:\\python-3.12.9\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'E:\\python-3.12.9\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'E:\\python-3.12.9\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'E:\\python-3.12.9\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'E:\\python-3.12.9\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'E:\\python-3.12.9\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'E:\\python-3.12.9\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'E:\\python-3.12.9\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'E:\\python-3.12.9\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'E:\\python-3.12.9\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'E:\\python-3.12.9\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'E:\\python-3.12.9\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'E:\\python-3.12.9\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'E:\\python-3.12.9\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'E:\\python-3.12.9\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'E:\\python-3.12.9\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'E:\\python-3.12.9\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'E:\\python-3.12.9\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'E:\\python-3.12.9\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'E:\\python-3.12.9\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'E:\\python-3.12.9\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'E:\\python-3.12.9\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'E:\\python-3.12.9\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'E:\\python-3.12.9\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'E:\\python-3.12.9\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'E:\\python-3.12.9\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'E:\\python-3.12.9\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'E:\\python-3.12.9\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'E:\\python-3.12.9\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'E:\\python-3.12.9\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'E:\\python-3.12.9\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'E:\\python-3.12.9\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'E:\\python-3.12.9\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'E:\\python-3.12.9\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'E:\\python-3.12.9\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'E:\\python-3.12.9\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'E:\\python-3.12.9\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'E:\\python-3.12.9\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'E:\\python-3.12.9\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'E:\\python-3.12.9\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'E:\\python-3.12.9\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'E:\\python-3.12.9\\Lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'E:\\python-3.12.9\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'E:\\python-3.12.9\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'E:\\python-3.12.9\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pythoncom',
   'E:\\python-3.12.9\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'E:\\python-3.12.9\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'E:\\python-3.12.9\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'E:\\python-3.12.9\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'E:\\python-3.12.9\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'E:\\python-3.12.9\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'E:\\python-3.12.9\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'E:\\python-3.12.9\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\python-3.12.9\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'E:\\python-3.12.9\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'E:\\python-3.12.9\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'E:\\python-3.12.9\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'E:\\python-3.12.9\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'E:\\python-3.12.9\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'E:\\python-3.12.9\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'E:\\python-3.12.9\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('sqlite3', 'E:\\python-3.12.9\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__',
   'E:\\python-3.12.9\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2', 'E:\\python-3.12.9\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'E:\\python-3.12.9\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'E:\\python-3.12.9\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'E:\\python-3.12.9\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'E:\\python-3.12.9\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'E:\\python-3.12.9\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'E:\\python-3.12.9\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'E:\\python-3.12.9\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'E:\\python-3.12.9\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'E:\\python-3.12.9\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'E:\\python-3.12.9\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'E:\\python-3.12.9\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'E:\\python-3.12.9\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'E:\\python-3.12.9\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'E:\\python-3.12.9\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser',
   'E:\\python-3.12.9\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re', 'E:\\python-3.12.9\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'E:\\python-3.12.9\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'E:\\python-3.12.9\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'E:\\python-3.12.9\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'E:\\python-3.12.9\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('ui', 'e:\\VScodexiangmu\\xunileidian2\\ui\\__init__.py', 'PYMODULE'),
  ('ui.basic_config_ui',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\basic_config_ui.py',
   'PYMODULE'),
  ('ui.instagram_dm_ui',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\instagram_dm_ui.py',
   'PYMODULE'),
  ('ui.instagram_follow_ui',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\instagram_follow_ui.py',
   'PYMODULE'),
  ('ui.main_window_v2',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\main_window_v2.py',
   'PYMODULE'),
  ('ui.settings_ui',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\settings_ui.py',
   'PYMODULE'),
  ('ui.style_manager',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\style_manager.py',
   'PYMODULE'),
  ('ui.styled_widgets',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\styled_widgets.py',
   'PYMODULE'),
  ('ui.ui_service_layer',
   'e:\\VScodexiangmu\\xunileidian2\\ui\\ui_service_layer.py',
   'PYMODULE'),
  ('unittest', 'E:\\python-3.12.9\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'E:\\python-3.12.9\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'E:\\python-3.12.9\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'E:\\python-3.12.9\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'E:\\python-3.12.9\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'E:\\python-3.12.9\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'E:\\python-3.12.9\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result',
   'E:\\python-3.12.9\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'E:\\python-3.12.9\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'E:\\python-3.12.9\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite', 'E:\\python-3.12.9\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'E:\\python-3.12.9\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'E:\\python-3.12.9\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'E:\\python-3.12.9\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'E:\\python-3.12.9\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'E:\\python-3.12.9\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response',
   'E:\\python-3.12.9\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('webbrowser', 'E:\\python-3.12.9\\Lib\\webbrowser.py', 'PYMODULE'),
  ('win32com',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wmi', 'E:\\python-3.12.9\\Lib\\site-packages\\wmi.py', 'PYMODULE'),
  ('xml', 'E:\\python-3.12.9\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'E:\\python-3.12.9\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'E:\\python-3.12.9\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'E:\\python-3.12.9\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'E:\\python-3.12.9\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('yaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'E:\\python-3.12.9\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'E:\\python-3.12.9\\Lib\\zipimport.py', 'PYMODULE')])
