# coding=utf-8
import sys
import threading
import time

from authsdk import Auth


def work(auth):
    """
    删除该函数内的示例代码，将您的脚本写到此处

    超级防破解实现：
    1.关键性参数使用远程变量读写
    2.把涉及计算、拼接相关的代码封装成远程函数在云端计算
    云端数据的每一次调用都需要签名和时间鉴权，因此不管抓包还是爆破都没有任何用处
    """
    print('卡号备注：' + auth.remark)

    fun_res = auth.run_function('远程函数示例', '100, 200')
    if fun_res or fun_res == '':
        print('远程函数执行的结果是：' + fun_res)

    for i in range(1000):
        time.sleep(1)
        print(f'我是主脚本，已运行{i}秒')


if __name__ == '__main__':
    print('注意：务必将三条认证线路采用选择框或单选框的形式给用户自主选择当前最佳的线路使用，可参考QT界面全功能源码')
    auth = Auth()

    result = auth.appinfo(0)
    code = auth.rsa_decrypt(result['code'])
    if code == '200':
        print('-' * 15, '这部分代码目的是演示如何得到这些参数，可删除用不到的部分', '-' * 15)
        app_name = auth.rsa_decrypt(result['appName'])
        print('项目名称：' + app_name)
        version = auth.rsa_decrypt(result['version'])
        print('后台版本：' + version)
        app_status = auth.rsa_decrypt(result['appStatus'])
        print('项目开关：' + app_status)
        notice_status = auth.rsa_decrypt(result['noticeStatus'])
        print('公告开关：' + notice_status)
        trial_count = auth.rsa_decrypt(result['trialCount'])
        print('试用次数：' + trial_count)
        trial_time = auth.rsa_decrypt(result['trialTime'])
        print('试用时长：' + trial_time)
        change_time = auth.rsa_decrypt(result['changeTime'])
        print('顶号扣时：' + change_time)
        web_site = auth.rsa_decrypt(result['webSite'])
        print('官方网址：' + web_site)
        shop_link = auth.rsa_decrypt(result['shopLink'])
        print('专属网店：' + shop_link)
        download_link = auth.rsa_decrypt(result['downloadLink'])
        print('下载网址：' + download_link)
        notice = auth.rsa_decrypt(result['notice'])
        print('公告信息：' + notice)
        contact_info = auth.rsa_decrypt(result['contactInfo'])
        print('客服信息：' + contact_info)

        # 检测更新
        if float(version) > auth.version and auth.is_valid_url(download_link):
            print('检测到新版本自动打开下载网址')
            auth.open_url(download_link)
            if auth.update_mode == 1:
                sys.exit()

    # 网络验证
    auth_code = input('请输入或粘贴您的卡号：')  # 可通过UI接收用户输入的卡密 或 把卡密保存到文本读取
    result = auth.verify(auth_code)  # 单卡号模式只需传入卡号；如果账号密码认证，第一个参数为账号，第二个参数为密码
    print(result['msg'])
    if result['code'] == 200:
        # 认证成功，启动主脚本
        work_thread = threading.Thread(target=work, kwargs={'auth': auth}, daemon=True)
        work_thread.start()
        # 调用心跳轮询
        auth.heart_beat()
    elif result['code'] == 104:
        # 检测到换机则自动调用换机相关方法
        auth.change_device(auth_code)
    else:
        sys.exit()
