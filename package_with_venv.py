#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 虚拟环境打包脚本 - 雷电模拟器中控系统
========================================
功能描述: 在虚拟环境中打包应用程序
主要功能: 检查虚拟环境、安装PyInstaller、执行打包
使用方法: 在激活虚拟环境后运行 python package_with_venv.py
注意事项: 必须在激活的虚拟环境中运行
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 雷电模拟器中控系统 - 虚拟环境打包")
    print("=" * 60)

def check_virtual_environment():
    """检查是否在虚拟环境中"""
    print("🔍 检查虚拟环境状态...")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or  # virtualenv
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)  # venv
    )
    
    if in_venv:
        print(f"✅ 当前在虚拟环境中: {sys.prefix}")
        return True
    else:
        print("❌ 未检测到虚拟环境")
        print("💡 请先激活虚拟环境:")
        if os.name == 'nt':
            print("   Windows: activate_venv.bat")
        else:
            print("   Unix/Linux: source activate_venv.sh")
        return False

def check_required_files():
    """检查必要文件"""
    print("\n🔍 检查必要文件...")
    
    project_root = Path(__file__).parent
    
    required_files = {
        "main.py": "主程序文件",
        "img": "图片资源目录",
        "app_config.json": "配置文件",
        "core": "核心模块目录",
        "ui": "界面模块目录"
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        full_path = project_root / file_path
        if full_path.exists():
            if file_path == "img":
                png_count = len(list(full_path.glob("*.png")))
                print(f"   ✅ {description} ({png_count}个图片)")
            elif full_path.is_dir():
                py_count = len(list(full_path.glob("*.py")))
                print(f"   ✅ {description} ({py_count}个Python文件)")
            else:
                print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    return True

def install_pyinstaller():
    """安装或检查PyInstaller"""
    print("\n📦 检查PyInstaller...")
    
    try:
        import PyInstaller
        print("   ✅ PyInstaller已安装")
        return True
    except ImportError:
        print("   📥 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                         check=True, capture_output=True, text=True)
            print("   ✅ PyInstaller安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"   ❌ PyInstaller安装失败: {e}")
            return False

def clean_build_files():
    """清理构建文件"""
    print("\n🗑️  清理旧的构建文件...")
    
    project_root = Path(__file__).parent
    
    # 要清理的目录和文件
    clean_targets = [
        project_root / "build",
        project_root / "dist", 
        project_root / "ins雷电中控.spec"
    ]
    
    for target in clean_targets:
        if target.exists():
            if target.is_dir():
                shutil.rmtree(target)
                print(f"   🗑️  已删除目录: {target.name}")
            else:
                target.unlink()
                print(f"   🗑️  已删除文件: {target.name}")

def create_pyinstaller_command():
    """创建PyInstaller命令"""
    project_root = Path(__file__).parent
    
    # 使用绝对路径确保文件被正确包含
    img_path = str(project_root / "img")
    config_path = str(project_root / "config") 
    data_path = str(project_root / "data")
    core_path = str(project_root / "core")
    ui_path = str(project_root / "ui")
    app_config_path = str(project_root / "app_config.json")
    
    cmd = [
        "pyinstaller",
        "--onefile",                              # 单文件打包
        "--windowed",                             # 隐藏控制台窗口
        "--clean",                                # 清理缓存
        "--name=ins雷电中控",                     # 程序名称
        f"--add-data={img_path};img",             # 图片资源
        f"--add-data={config_path};config",       # 配置目录
        f"--add-data={data_path};data",           # 数据目录
        f"--add-data={core_path};core",           # 核心模块
        f"--add-data={ui_path};ui",               # UI模块
        f"--add-data={app_config_path};.",        # 配置文件
        "--hidden-import=PyQt6.QtCore",           # PyQt6核心
        "--hidden-import=PyQt6.QtGui",            # PyQt6界面
        "--hidden-import=PyQt6.QtWidgets",        # PyQt6控件
        "--hidden-import=requests",               # HTTP请求
        "--hidden-import=psutil",                 # 系统监控
        "--hidden-import=sqlite3",                # 数据库
        "--hidden-import=asyncio",                # 异步编程
        "--hidden-import=PIL",                    # 图像处理
        "--hidden-import=cv2",                    # OpenCV
        "--collect-all=PyQt6",                    # 收集所有PyQt6模块
        "main.py"                                 # 主程序
    ]
    
    return cmd

def execute_packaging():
    """执行打包"""
    print("\n🔨 开始打包...")
    
    project_root = Path(__file__).parent
    cmd = create_pyinstaller_command()
    
    print("📋 打包命令:")
    print("   " + " ".join(cmd))
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包完成")
            return True
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def verify_package():
    """验证打包结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        # 检查是否可以运行（简单测试）
        print("   🧪 测试可执行文件...")
        try:
            # 尝试运行程序（带超时，避免卡住）
            test_result = subprocess.run([str(exe_file), "--help"], 
                                       capture_output=True, text=True, timeout=10)
            print("   ✅ 可执行文件测试通过")
        except subprocess.TimeoutExpired:
            print("   ⚠️  可执行文件测试超时（可能正常）")
        except Exception as e:
            print(f"   ⚠️  可执行文件测试异常: {e}")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def print_success_info():
    """打印成功信息"""
    print("\n" + "=" * 60)
    print("🎉 打包成功完成！")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    print(f"\n📦 输出文件: {exe_file}")
    print(f"📁 输出目录: {exe_file.parent}")
    
    print("\n💡 使用说明:")
    print("1. 可执行文件位于 dist/ 目录中")
    print("2. 可以直接运行 ins雷电中控.exe")
    print("3. 可以将exe文件分发给其他用户")
    print("4. 无需安装Python环境即可运行")
    
    print("\n⚠️  注意事项:")
    print("- 首次运行可能需要较长时间")
    print("- 确保目标机器有必要的系统库")
    print("- 建议在目标环境中测试运行")

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查虚拟环境
        if not check_virtual_environment():
            return False
        
        # 检查必要文件
        if not check_required_files():
            return False
        
        # 询问是否清理旧文件
        clean_choice = input("\n是否清理旧的构建文件? (y/n, 默认y): ").strip().lower()
        if clean_choice != 'n':
            clean_build_files()
        
        # 安装PyInstaller
        if not install_pyinstaller():
            return False
        
        # 执行打包
        if not execute_packaging():
            return False
        
        # 验证打包结果
        if not verify_package():
            return False
        
        # 打印成功信息
        print_success_info()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
        input("按Enter键退出...")
        sys.exit(1)
    else:
        input("\n按Enter键退出...")
