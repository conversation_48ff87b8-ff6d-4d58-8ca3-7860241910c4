#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 快速打包脚本 - 雷电模拟器中控系统
========================================
功能描述: 使用当前环境快速打包程序
使用方法: python quick_package.py
注意事项: 在虚拟环境中运行效果更好
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 雷电模拟器中控系统 - 快速打包")
    print("=" * 60)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"   Python路径: {sys.executable}")
    print(f"   Python版本: {sys.version.split()[0]}")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("   ✅ 在虚拟环境中")
    else:
        print("   ⚠️  不在虚拟环境中（建议使用虚拟环境）")

def install_pyinstaller():
    """安装PyInstaller"""
    print("\n📦 检查PyInstaller...")
    
    try:
        import PyInstaller
        print("   ✅ PyInstaller已安装")
        return True
    except ImportError:
        print("   📥 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                         check=True, capture_output=True, text=True)
            print("   ✅ PyInstaller安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"   ❌ PyInstaller安装失败: {e}")
            return False

def clean_build():
    """清理构建文件"""
    print("\n🗑️  清理旧的构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
        project_root / "ins雷电中控.spec"
    ]
    
    for target in clean_targets:
        if target.exists():
            if target.is_dir():
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            else:
                target.unlink()
                print(f"   🗑️  已删除: {target.name}")

def create_simple_package():
    """创建简单打包"""
    print("\n🔨 开始打包...")
    
    project_root = Path(__file__).parent
    
    # 简化的打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",          # 🔧 添加WMI模块
        "--hidden-import=sqlite3",      # 数据库模块
        "--hidden-import=asyncio",      # 异步模块
        "main.py"
    ]
    
    print("📋 执行命令:")
    print("   " + " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包完成")
            return True
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查环境
        check_environment()
        
        # 安装PyInstaller
        if not install_pyinstaller():
            return False
        
        # 清理构建文件
        clean_build()
        
        # 执行打包
        if not create_simple_package():
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 打包成功完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
