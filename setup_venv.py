#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 虚拟环境设置脚本 - 雷电模拟器中控系统
========================================
功能描述: 自动创建虚拟环境并安装所需依赖
主要功能: 创建venv、安装requirements.txt、验证安装
使用方法: python setup_venv.py
注意事项: 需要Python 3.8+版本
========================================
"""

import os
import sys
import subprocess
import venv
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🎯 雷电模拟器中控系统 - 虚拟环境设置")
    print("=" * 60)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本检查通过")
    return True

def create_virtual_environment():
    """创建虚拟环境"""
    print("\n📦 创建虚拟环境...")
    
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    # 如果虚拟环境已存在，询问是否重新创建
    if venv_path.exists():
        response = input("⚠️  虚拟环境已存在，是否重新创建? (y/n, 默认n): ").strip().lower()
        if response == 'y':
            import shutil
            shutil.rmtree(venv_path)
            print("🗑️  已删除旧的虚拟环境")
        else:
            print("📁 使用现有虚拟环境")
            return venv_path
    
    try:
        # 创建虚拟环境
        venv.create(venv_path, with_pip=True)
        print(f"✅ 虚拟环境创建成功: {venv_path}")
        return venv_path
    except Exception as e:
        print(f"❌ 创建虚拟环境失败: {e}")
        return None

def get_venv_python(venv_path):
    """获取虚拟环境中的Python路径"""
    if os.name == 'nt':  # Windows
        return venv_path / "Scripts" / "python.exe"
    else:  # Unix/Linux/macOS
        return venv_path / "bin" / "python"

def get_venv_pip(venv_path):
    """获取虚拟环境中的pip路径"""
    if os.name == 'nt':  # Windows
        return venv_path / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        return venv_path / "bin" / "pip"

def upgrade_pip(venv_path):
    """升级pip"""
    print("\n🔧 升级pip...")
    
    pip_path = get_venv_pip(venv_path)
    python_path = get_venv_python(venv_path)
    
    try:
        subprocess.run([str(python_path), "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True, text=True)
        print("✅ pip升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  pip升级失败: {e}")
        return False

def install_requirements(venv_path):
    """安装依赖包"""
    print("\n📥 安装依赖包...")
    
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ 未找到requirements.txt文件")
        return False
    
    python_path = get_venv_python(venv_path)
    
    try:
        # 安装requirements.txt中的包
        result = subprocess.run([
            str(python_path), "-m", "pip", "install", 
            "-r", str(requirements_file)
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖包安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败:")
        print(f"   错误信息: {e.stderr}")
        return False

def verify_installation(venv_path):
    """验证关键包安装"""
    print("\n🔍 验证关键包安装...")
    
    python_path = get_venv_python(venv_path)
    
    # 关键包列表
    key_packages = [
        ("PyQt6", "PyQt6图形界面框架"),
        ("requests", "HTTP请求库"),
        ("Pillow", "图像处理库"),
        ("psutil", "系统监控库"),
        ("pyinstaller", "打包工具")
    ]
    
    all_ok = True
    
    for package, description in key_packages:
        try:
            result = subprocess.run([
                str(python_path), "-c", f"import {package}; print('{package} OK')"
            ], check=True, capture_output=True, text=True)
            print(f"   ✅ {description}")
        except subprocess.CalledProcessError:
            print(f"   ❌ {description} - 安装失败")
            all_ok = False
    
    return all_ok

def create_activation_scripts(venv_path):
    """创建激活脚本"""
    print("\n📝 创建激活脚本...")
    
    project_root = Path(__file__).parent
    
    # Windows激活脚本
    if os.name == 'nt':
        activate_script = project_root / "activate_venv.bat"
        with open(activate_script, 'w', encoding='utf-8') as f:
            f.write(f"""@echo off
echo 🎯 激活雷电模拟器中控系统虚拟环境
call "{venv_path}\\Scripts\\activate.bat"
echo ✅ 虚拟环境已激活
echo 💡 使用 'deactivate' 命令退出虚拟环境
cmd /k
""")
        print(f"   ✅ Windows激活脚本: {activate_script}")
    
    # Unix/Linux激活脚本
    activate_script_sh = project_root / "activate_venv.sh"
    with open(activate_script_sh, 'w', encoding='utf-8') as f:
        f.write(f"""#!/bin/bash
echo "🎯 激活雷电模拟器中控系统虚拟环境"
source "{venv_path}/bin/activate"
echo "✅ 虚拟环境已激活"
echo "💡 使用 'deactivate' 命令退出虚拟环境"
bash
""")
    
    # 设置执行权限
    try:
        os.chmod(activate_script_sh, 0o755)
        print(f"   ✅ Unix/Linux激活脚本: {activate_script_sh}")
    except:
        print(f"   ⚠️  Unix/Linux激活脚本创建完成，但无法设置执行权限")

def print_usage_instructions(venv_path):
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("🎉 虚拟环境设置完成！")
    print("=" * 60)
    
    print("\n📋 使用说明:")
    print("1. 激活虚拟环境:")
    
    if os.name == 'nt':  # Windows
        print(f"   Windows: activate_venv.bat")
        print(f"   或手动: {venv_path}\\Scripts\\activate.bat")
    else:  # Unix/Linux/macOS
        print(f"   Unix/Linux: source activate_venv.sh")
        print(f"   或手动: source {venv_path}/bin/activate")
    
    print("\n2. 运行程序:")
    print("   python main.py")
    
    print("\n3. 打包程序:")
    print("   python package.py")
    
    print("\n4. 退出虚拟环境:")
    print("   deactivate")
    
    print("\n💡 提示:")
    print("   - 每次开发前都需要先激活虚拟环境")
    print("   - 虚拟环境路径: " + str(venv_path))

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查Python版本
        if not check_python_version():
            return False
        
        # 创建虚拟环境
        venv_path = create_virtual_environment()
        if not venv_path:
            return False
        
        # 升级pip
        upgrade_pip(venv_path)
        
        # 安装依赖包
        if not install_requirements(venv_path):
            return False
        
        # 验证安装
        if not verify_installation(venv_path):
            print("⚠️  部分包安装可能有问题，但可以继续使用")
        
        # 创建激活脚本
        create_activation_scripts(venv_path)
        
        # 打印使用说明
        print_usage_instructions(venv_path)
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 虚拟环境设置失败")
        input("按Enter键退出...")
        sys.exit(1)
    else:
        input("\n按Enter键退出...")
