#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import sys
from pathlib import Path

# 添加项目路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

def test_step_by_step():
    print("🎯 逐步测试Auth创建过程")
    
    start = time.time()
    print(f"[{time.time() - start:.3f}s] 开始导入authsdk")
    
    from core.authsdk import Auth
    print(f"[{time.time() - start:.3f}s] authsdk导入完成")
    
    print(f"[{time.time() - start:.3f}s] 开始创建Auth实例")
    auth = Auth()
    print(f"[{time.time() - start:.3f}s] Auth实例创建完成")
    
    print(f"设备ID: {auth.device_id[:20] if auth.device_id else 'None'}...")
    print(f"API: {auth.api}")
    print(f"项目ID: {auth.appid}")

if __name__ == "__main__":
    test_step_by_step()
