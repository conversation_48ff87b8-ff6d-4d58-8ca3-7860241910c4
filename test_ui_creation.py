#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 测试UI创建是否有异常
========================================
功能描述: 测试Instagram DM UI的创建过程，检查是否有异常导致统计区域不显示
========================================
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.resolve()
sys.path.insert(0, str(PROJECT_ROOT))

def test_ui_creation():
    """测试UI创建过程"""
    print("🔍 测试Instagram DM UI创建过程...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.instagram_dm_ui import InstagramDMUI
        
        # 创建应用程序
        app = QApplication(sys.argv if sys.argv else ['test'])
        
        print("  📋 创建InstagramDMUI实例...")
        ui = InstagramDMUI()
        
        print("  📋 检查全局统计汇总区域是否创建...")
        if hasattr(ui, 'total_follow_success_card'):
            print("  ✅ total_follow_success_card 已创建")
        else:
            print("  ❌ total_follow_success_card 未创建")
            return False
            
        if hasattr(ui, 'total_dm_success_card'):
            print("  ✅ total_dm_success_card 已创建")
        else:
            print("  ❌ total_dm_success_card 未创建")
            return False
            
        if hasattr(ui, 'active_emulators_card'):
            print("  ✅ active_emulators_card 已创建")
        else:
            print("  ❌ active_emulators_card 未创建")
            return False
            
        if hasattr(ui, 'last_update_card'):
            print("  ✅ last_update_card 已创建")
        else:
            print("  ❌ last_update_card 未创建")
            return False
        
        print("  📋 检查刷新按钮是否创建...")
        if hasattr(ui, 'refresh_summary_btn'):
            print("  ✅ refresh_summary_btn 已创建")
        else:
            print("  ❌ refresh_summary_btn 未创建")
            return False
        
        print("  📋 检查定时器是否创建...")
        if hasattr(ui, 'summary_timer'):
            print("  ✅ summary_timer 已创建")
            print(f"    定时器间隔: {ui.summary_timer.interval()}ms")
            print(f"    定时器活跃: {ui.summary_timer.isActive()}")
        else:
            print("  ❌ summary_timer 未创建")
            return False
        
        print("  📋 测试统计数据更新方法...")
        try:
            ui._update_global_summary()
            print("  ✅ _update_global_summary() 方法执行成功")
        except Exception as e:
            print(f"  ❌ _update_global_summary() 方法执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("  📋 检查UI布局...")
        layout = ui.layout()
        if layout:
            print(f"  ✅ 主布局已创建，子控件数量: {layout.count()}")
            for i in range(layout.count()):
                widget = layout.itemAt(i).widget()
                if widget:
                    print(f"    - 控件{i}: {widget.__class__.__name__}")
                    if hasattr(widget, 'title'):
                        print(f"      标题: {widget.title()}")

                    # 如果是滚动区域，检查其内容
                    if widget.__class__.__name__ == 'QScrollArea':
                        scroll_widget = widget.widget()
                        if scroll_widget:
                            print(f"      滚动内容: {scroll_widget.__class__.__name__}")
                            scroll_layout = scroll_widget.layout()
                            if scroll_layout:
                                print(f"      滚动内容子控件数量: {scroll_layout.count()}")
                                for j in range(scroll_layout.count()):
                                    item = scroll_layout.itemAt(j)
                                    if item and item.widget():
                                        sub_widget = item.widget()
                                        print(f"        - 子控件{j}: {sub_widget.__class__.__name__}")
                                        if hasattr(sub_widget, 'title'):
                                            print(f"          标题: {sub_widget.title()}")
                        else:
                            print("      ❌ 滚动区域没有内容控件")
        else:
            print("  ❌ 主布局未创建")
            return False
        
        print("  ✅ UI创建测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ UI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 开始测试UI创建")
    print("=" * 60)
    
    if test_ui_creation():
        print("\n🎉 UI创建测试通过！")
        print("\n📝 可能的问题:")
        print("1. UI已正常创建，但可能被其他控件遮挡")
        print("2. 窗口大小不够，统计区域被滚动条隐藏")
        print("3. 样式问题导致统计区域不可见")
        print("\n💡 建议:")
        print("- 尝试调整窗口大小")
        print("- 检查是否有滚动条")
        print("- 查看控制台是否有错误信息")
        return 0
    else:
        print("\n❌ UI创建测试失败！")
        print("需要检查代码中的异常")
        return 1

if __name__ == "__main__":
    sys.exit(main())
