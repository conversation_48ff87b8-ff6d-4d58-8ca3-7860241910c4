#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 虚拟环境测试脚本
========================================
功能描述: 测试虚拟环境中的包安装情况
使用方法: 在虚拟环境中运行 python test_venv.py
========================================
"""

import sys
import subprocess
from pathlib import Path

def test_packages():
    """测试包安装情况"""
    print("🧪 测试虚拟环境中的包...")
    print(f"Python路径: {sys.executable}")
    print(f"Python版本: {sys.version}")
    
    # 测试包列表
    test_packages = [
        ("PyQt6", "PyQt6图形界面框架"),
        ("requests", "HTTP请求库"),
        ("PIL", "图像处理库（Pillow）"),
        ("psutil", "系统监控库"),
        ("PyInstaller", "打包工具")
    ]
    
    print("\n📦 包测试结果:")
    working_count = 0
    
    for package, description in test_packages:
        try:
            __import__(package)
            print(f"   ✅ {description}")
            working_count += 1
        except ImportError as e:
            print(f"   ❌ {description} - {str(e)}")
    
    print(f"\n📊 测试结果: {working_count}/{len(test_packages)} 个包可用")
    
    if working_count >= 3:
        print("✅ 虚拟环境基本可用")
        return True
    else:
        print("❌ 虚拟环境存在问题")
        return False

def test_pyinstaller():
    """测试PyInstaller"""
    print("\n🔨 测试PyInstaller...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller", "--version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   ✅ PyInstaller版本: {result.stdout.strip()}")
            return True
        else:
            print(f"   ❌ PyInstaller测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ PyInstaller测试异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 虚拟环境测试")
    print("=" * 50)
    
    # 测试包安装
    packages_ok = test_packages()
    
    # 测试PyInstaller
    pyinstaller_ok = test_pyinstaller()
    
    print("\n" + "=" * 50)
    if packages_ok and pyinstaller_ok:
        print("🎉 虚拟环境测试全部通过！")
        print("💡 可以开始打包程序了")
    elif packages_ok:
        print("⚠️  虚拟环境基本可用，但PyInstaller有问题")
        print("💡 可以尝试重新安装PyInstaller")
    else:
        print("❌ 虚拟环境存在严重问题")
        print("💡 建议重新创建虚拟环境")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
