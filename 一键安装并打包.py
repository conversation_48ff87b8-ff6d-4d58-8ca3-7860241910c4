#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 一键安装依赖并打包程序
========================================
功能描述: 自动安装所有必要依赖，然后进行打包
使用方法: python 一键安装并打包.py
注意事项: 确保在虚拟环境中运行
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 一键安装依赖并打包程序")
    print("=" * 60)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"   Python路径: {sys.executable}")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("   ✅ 在虚拟环境中")
        return True
    else:
        print("   ❌ 不在虚拟环境中")
        print("   💡 请先激活虚拟环境:")
        print("      Windows: venv\\Scripts\\activate.bat")
        print("      Linux/Mac: source venv/bin/activate")
        return False

def install_essential_packages():
    """安装必要的包"""
    print("\n📦 安装必要的包...")
    
    # 基于实际代码分析的必需包
    essential_packages = [
        "PyQt6",           # GUI框架
        "requests",        # HTTP请求
        "pycryptodome",    # 加密功能
        "psutil",          # 系统监控
        "wmi",             # Windows管理
        "Pillow",          # 图像处理
        "pyinstaller"      # 打包工具
    ]
    
    success_count = 0
    
    for package in essential_packages:
        print(f"   📥 安装 {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package, "--upgrade"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"   ✅ {package} 安装成功")
                success_count += 1
            else:
                print(f"   ❌ {package} 安装失败")
                if result.stderr:
                    print(f"      错误: {result.stderr[:100]}...")
        except Exception as e:
            print(f"   ❌ {package} 安装异常: {e}")
    
    print(f"\n📊 安装结果: {success_count}/{len(essential_packages)} 个包安装成功")
    
    if success_count >= len(essential_packages) - 1:  # 允许1个包失败
        print("✅ 依赖安装基本完成")
        return True
    else:
        print("❌ 依赖安装失败过多")
        return False

def verify_packages():
    """验证包安装"""
    print("\n🔍 验证包安装...")
    
    test_imports = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("requests", "requests"),
        ("Crypto", "Crypto.Cipher"),
        ("psutil", "psutil"),
        ("wmi", "wmi"),
        ("PIL", "PIL"),
        ("PyInstaller", "PyInstaller")
    ]
    
    working_count = 0
    
    for display_name, import_name in test_imports:
        try:
            __import__(import_name)
            print(f"   ✅ {display_name}")
            working_count += 1
        except ImportError:
            print(f"   ❌ {display_name}")
    
    print(f"\n📊 验证结果: {working_count}/{len(test_imports)} 个包可用")
    return working_count >= len(test_imports) - 1

def clear_build():
    """清理构建文件"""
    print("\n🗑️  清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")

def create_package():
    """创建打包"""
    print("\n🔨 开始打包...")
    
    project_root = Path(__file__).parent
    
    # 精简的打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 必要的隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "--hidden-import=PIL",
        "main.py"
    ]
    
    print("📋 打包命令:")
    print("   " + " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功")
            return True
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        if size_mb < 80:
            print("   🎉 文件大小理想！")
        else:
            print("   ⚠️  文件较大，但应该包含所有功能")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查环境
        if not check_environment():
            return False
        
        # 安装依赖
        if not install_essential_packages():
            print("\n💥 依赖安装失败")
            return False
        
        # 验证安装
        if not verify_packages():
            print("\n⚠️  部分包验证失败，但继续打包...")
        
        # 清理构建文件
        clear_build()
        
        # 执行打包
        if not create_package():
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 一键安装并打包完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 现在exe文件包含所有必要依赖，可以独立运行")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 操作失败")
    
    input("\n按Enter键退出...")
