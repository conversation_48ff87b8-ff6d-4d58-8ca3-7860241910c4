#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 一键完成：安装依赖 + 打包程序
========================================
功能描述: 一个脚本完成所有操作，无需分步执行
使用方法: python 一键完成.py
注意事项: 自动检测环境，强制使用虚拟环境，完成所有步骤
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 一键完成：安装依赖 + 打包程序")
    print("=" * 60)
    print("💡 这个脚本会自动完成所有步骤，请耐心等待...")
    print()

def get_venv_info():
    """获取虚拟环境信息"""
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    if not venv_path.exists():
        print("❌ 未找到venv目录，请先创建虚拟环境")
        return None
    
    # Windows路径
    venv_python = venv_path / "Scripts" / "python.exe"
    venv_pip = venv_path / "Scripts" / "pip.exe"
    venv_pyinstaller = venv_path / "Scripts" / "pyinstaller.exe"
    
    if not venv_python.exists():
        print(f"❌ 虚拟环境Python不存在: {venv_python}")
        return None
    
    return {
        'python': str(venv_python),
        'pip': str(venv_pip),
        'pyinstaller': str(venv_pyinstaller),
        'venv_path': str(venv_path),
        'scripts_dir': str(venv_path / 'Scripts')
    }

def step1_install_dependencies(venv_info):
    """步骤1: 安装所有依赖"""
    print("📦 步骤1: 安装所有依赖...")
    
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    
    if not requirements_file.exists():
        print("   ❌ 未找到requirements.txt文件")
        return False
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print("   📥 正在安装requirements.txt中的所有包...")
        print("   ⏳ 这可能需要几分钟时间，请耐心等待...")
        
        # 安装requirements.txt
        cmd = [
            venv_info['python'], 
            "-m", "pip", "install", 
            "-r", str(requirements_file),
            "--upgrade",
            "--no-cache-dir"
        ]
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("   ✅ 所有依赖安装成功")
            return True
        else:
            print("   ❌ 依赖安装失败")
            if result.stderr:
                print(f"   错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 安装超时，但可能仍在继续...")
        return False
    except Exception as e:
        print(f"   ❌ 安装异常: {e}")
        return False

def step2_verify_packages(venv_info):
    """步骤2: 验证关键包"""
    print("\n🔍 步骤2: 验证关键包...")
    
    key_packages = [
        'PyQt6', 'requests', 'Pillow', 'psutil', 'wmi', 
        'pycryptodome', 'pyinstaller', 'opencv-python'
    ]
    
    working_packages = []
    
    for pkg in key_packages:
        try:
            if pkg == 'pycryptodome':
                test_cmd = [venv_info['python'], "-c", "import Crypto; print('OK')"]
            elif pkg == 'Pillow':
                test_cmd = [venv_info['python'], "-c", "import PIL; print('OK')"]
            else:
                test_cmd = [venv_info['python'], "-c", f"import {pkg}; print('OK')"]
            
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"   ✅ {pkg}")
                working_packages.append(pkg)
            else:
                print(f"   ❌ {pkg}")
        except:
            print(f"   ❌ {pkg}")
    
    success_rate = len(working_packages) / len(key_packages)
    print(f"\n   📊 验证结果: {len(working_packages)}/{len(key_packages)} ({success_rate:.1%})")
    
    return success_rate >= 0.8  # 80%成功率即可

def step3_clear_build():
    """步骤3: 清理构建文件"""
    print("\n🗑️  步骤3: 清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")
    
    # 清理spec文件
    for spec_file in project_root.glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"   🗑️  已删除: {spec_file.name}")
        except:
            pass

def step4_package(venv_info):
    """步骤4: 执行打包"""
    print("\n🔨 步骤4: 执行打包...")
    
    project_root = Path(__file__).parent
    
    # 完整的打包命令
    cmd = [
        venv_info['python'], "-m", "PyInstaller",  # 使用python -m方式调用
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 包含所有可能需要的隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",
        "--hidden-import=Crypto",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "--hidden-import=PIL",
        "--hidden-import=cv2",
        "--hidden-import=numpy",
        "--hidden-import=yaml",
        "--hidden-import=colorlog",
        "--hidden-import=aiohttp",
        "--hidden-import=sqlite3",
        "main.py"
    ]
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print("   📋 开始打包...")
        print("   ⏳ 打包过程可能需要几分钟，请耐心等待...")
        
        result = subprocess.run(cmd, cwd=project_root, env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("   ✅ 打包成功")
            return True
        else:
            print("   ❌ 打包失败")
            if result.stderr:
                print(f"   错误: {result.stderr[:300]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 打包超时")
        return False
    except Exception as e:
        print(f"   ❌ 打包异常: {e}")
        return False

def step5_verify_result():
    """步骤5: 验证最终结果"""
    print("\n🔍 步骤5: 验证最终结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        if size_mb > 10:  # 至少10MB才算正常
            print("   🎉 打包成功，文件大小正常")
            return True
        else:
            print("   ⚠️  文件太小，可能打包不完整")
            return False
    else:
        print("   ❌ 未找到可执行文件")
        return False

def show_final_result(success):
    """显示最终结果"""
    print("\n" + "=" * 60)
    if success:
        print("🎉 一键完成成功！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 现在可以直接运行或分发给用户")
    else:
        print("💥 一键完成失败")
        print("💡 请检查上面的错误信息")
    print("=" * 60)

def main():
    """主函数"""
    try:
        print_header()
        
        # 获取虚拟环境信息
        print("🔍 检查虚拟环境...")
        venv_info = get_venv_info()
        if not venv_info:
            return False
        print("   ✅ 虚拟环境检查通过")
        
        # 步骤1: 安装依赖
        if not step1_install_dependencies(venv_info):
            print("\n💥 步骤1失败: 依赖安装失败")
            return False
        
        # 步骤2: 验证包
        if not step2_verify_packages(venv_info):
            print("\n⚠️  步骤2警告: 部分包验证失败，但继续打包...")
        
        # 步骤3: 清理构建文件
        step3_clear_build()
        
        # 步骤4: 执行打包
        if not step4_package(venv_info):
            print("\n💥 步骤4失败: 打包失败")
            return False
        
        # 步骤5: 验证结果
        if not step5_verify_result():
            print("\n💥 步骤5失败: 结果验证失败")
            return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    show_final_result(success)
    input("\n按Enter键退出...")
