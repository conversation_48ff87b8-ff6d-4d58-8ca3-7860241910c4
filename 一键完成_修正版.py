#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 一键完成：检查依赖 → 安装缺失 → 验证 → 打包
========================================
功能描述: 正确的逻辑流程，先检查再安装，验证失败则停止
使用方法: python 一键完成_修正版.py
注意事项: 验证失败会停止，不会盲目打包
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🚀 一键完成：检查 → 安装 → 验证 → 打包")
    print("=" * 60)
    print("💡 正确的逻辑流程，验证失败会停止")
    print()

def get_venv_info():
    """获取虚拟环境信息"""
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    if not venv_path.exists():
        print("❌ 未找到venv目录，请先创建虚拟环境")
        return None
    
    venv_python = venv_path / "Scripts" / "python.exe"
    if not venv_python.exists():
        print(f"❌ 虚拟环境Python不存在: {venv_python}")
        return None
    
    return {
        'python': str(venv_python),
        'venv_path': str(venv_path),
        'scripts_dir': str(venv_path / 'Scripts')
    }

def step1_check_dependencies(venv_info):
    """步骤1: 检查依赖包状态"""
    print("🔍 步骤1: 检查依赖包状态...")
    
    key_packages = [
        ('PyQt6', 'PyQt6', 'GUI框架'),
        ('requests', 'requests', 'HTTP请求'),
        ('Pillow', 'PIL', '图像处理'),
        ('psutil', 'psutil', '系统监控'),
        ('wmi', 'wmi', 'Windows管理'),
        ('pycryptodome', 'Crypto', '加密库'),
        ('pyinstaller', 'PyInstaller', '打包工具'),
        ('opencv-python', 'cv2', '计算机视觉'),
        ('numpy', 'numpy', '数值计算')
    ]
    
    missing_packages = []
    working_packages = []
    
    for pkg_name, import_name, description in key_packages:
        try:
            test_cmd = [venv_info['python'], "-c", f"import {import_name}; print('OK')"]
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"   ✅ {pkg_name:<15} - {description}")
                working_packages.append(pkg_name)
            else:
                print(f"   ❌ {pkg_name:<15} - {description} (缺失)")
                missing_packages.append(pkg_name)
        except:
            print(f"   ❌ {pkg_name:<15} - {description} (检查失败)")
            missing_packages.append(pkg_name)
    
    print(f"\n   📊 检查结果: {len(working_packages)}/{len(key_packages)} 个包可用")
    
    if missing_packages:
        print(f"   ⚠️  缺失包: {', '.join(missing_packages)}")
        return False, missing_packages
    else:
        print("   🎉 所有依赖包都已安装")
        return True, []

def step2_install_missing_packages(venv_info, missing_packages):
    """步骤2: 安装缺失的包"""
    print(f"\n📦 步骤2: 安装缺失的包 ({len(missing_packages)} 个)...")
    
    if not missing_packages:
        print("   ✅ 无需安装，所有包都已存在")
        return True
    
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print(f"   📥 正在安装: {', '.join(missing_packages)}")
        print("   ⏳ 这可能需要几分钟时间，请耐心等待...")
        
        # 使用requirements.txt安装所有包
        if requirements_file.exists():
            cmd = [
                venv_info['python'], 
                "-m", "pip", "install", 
                "-r", str(requirements_file),
                "--upgrade",
                "--no-cache-dir"
            ]
        else:
            # 否则单独安装缺失的包
            cmd = [
                venv_info['python'], 
                "-m", "pip", "install"
            ] + missing_packages + ["--upgrade", "--no-cache-dir"]
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("   ✅ 缺失包安装成功")
            return True
        else:
            print("   ❌ 缺失包安装失败")
            if result.stderr:
                print(f"   错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 安装超时")
        return False
    except Exception as e:
        print(f"   ❌ 安装异常: {e}")
        return False

def step3_verify_all_packages(venv_info):
    """步骤3: 验证所有包安装"""
    print("\n🔍 步骤3: 验证所有包安装...")
    
    key_packages = [
        ('PyQt6', 'PyQt6'),
        ('requests', 'requests'),
        ('Pillow', 'PIL'),
        ('psutil', 'psutil'),
        ('wmi', 'wmi'),
        ('pycryptodome', 'Crypto'),
        ('pyinstaller', 'PyInstaller'),
        ('opencv-python', 'cv2'),
        ('numpy', 'numpy')
    ]
    
    working_packages = []
    failed_packages = []
    
    for pkg_name, import_name in key_packages:
        try:
            test_cmd = [venv_info['python'], "-c", f"import {import_name}; print('OK')"]
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"   ✅ {pkg_name}")
                working_packages.append(pkg_name)
            else:
                print(f"   ❌ {pkg_name}")
                failed_packages.append(pkg_name)
        except:
            print(f"   ❌ {pkg_name}")
            failed_packages.append(pkg_name)
    
    success_rate = len(working_packages) / len(key_packages)
    print(f"\n   📊 最终验证结果: {len(working_packages)}/{len(key_packages)} ({success_rate:.1%})")
    
    if success_rate < 0.9:  # 要求90%成功率
        print(f"   ❌ 验证失败率过高，无法继续打包")
        print(f"   💥 失败的包: {', '.join(failed_packages)}")
        print("   💡 请检查安装错误或网络问题")
        return False
    
    print("   🎉 验证通过，可以开始打包")
    return True

def step4_clear_build():
    """步骤4: 清理构建文件"""
    print("\n🗑️  步骤4: 清理构建文件...")

    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]

    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")

    # 清理spec文件
    for spec_file in project_root.glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"   🗑️  已删除: {spec_file.name}")
        except:
            pass

def step5_package(venv_info):
    """步骤5: 执行打包"""
    print("\n🔨 步骤5: 执行打包...")

    project_root = Path(__file__).parent

    # 完整的打包命令
    cmd = [
        venv_info['python'], "-m", "PyInstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 包含所有必要的隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",
        "--hidden-import=Crypto",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "--hidden-import=PIL",
        "--hidden-import=cv2",
        "--hidden-import=numpy",
        "--hidden-import=yaml",
        "--hidden-import=colorlog",
        "--hidden-import=aiohttp",
        "--hidden-import=sqlite3",
        "main.py"
    ]

    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)

        print("   📋 开始打包...")
        print("   ⏳ 打包过程可能需要几分钟，请耐心等待...")

        result = subprocess.run(cmd, cwd=project_root, env=env, capture_output=True, text=True, timeout=600)

        if result.returncode == 0:
            print("   ✅ 打包成功")
            return True
        else:
            print("   ❌ 打包失败")
            if result.stderr:
                print(f"   错误: {result.stderr[:300]}...")
            return False

    except subprocess.TimeoutExpired:
        print("   ⏰ 打包超时")
        return False
    except Exception as e:
        print(f"   ❌ 打包异常: {e}")
        return False

def step6_verify_result():
    """步骤6: 验证最终结果"""
    print("\n🔍 步骤6: 验证最终结果...")

    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"

    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")

        if size_mb > 10:  # 至少10MB才算正常
            print("   🎉 打包成功，文件大小正常")
            return True
        else:
            print("   ⚠️  文件太小，可能打包不完整")
            return False
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()

        # 获取虚拟环境信息
        print("🔍 检查虚拟环境...")
        venv_info = get_venv_info()
        if not venv_info:
            return False
        print("   ✅ 虚拟环境检查通过")

        # 步骤1: 检查依赖
        all_installed, missing_packages = step1_check_dependencies(venv_info)

        # 步骤2: 安装缺失的包（如果有）
        if not all_installed:
            if not step2_install_missing_packages(venv_info, missing_packages):
                print("\n💥 步骤2失败: 缺失包安装失败")
                return False

        # 步骤3: 验证所有包
        if not step3_verify_all_packages(venv_info):
            print("\n💥 步骤3失败: 包验证失败，停止打包")
            print("💡 请检查上面的错误信息，修复后重试")
            return False

        # 步骤4: 清理构建文件
        step4_clear_build()

        # 步骤5: 执行打包
        if not step5_package(venv_info):
            print("\n💥 步骤5失败: 打包失败")
            return False

        # 步骤6: 验证结果
        if not step6_verify_result():
            print("\n💥 步骤6失败: 结果验证失败")
            return False

        return True

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_final_result(success):
    """显示最终结果"""
    print("\n" + "=" * 60)
    if success:
        print("🎉 一键完成成功！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 现在可以直接运行或分发给用户")
    else:
        print("💥 一键完成失败")
        print("💡 请检查上面的错误信息，修复后重试")
    print("=" * 60)

if __name__ == "__main__":
    success = main()
    show_final_result(success)
    input("\n按Enter键退出...")
