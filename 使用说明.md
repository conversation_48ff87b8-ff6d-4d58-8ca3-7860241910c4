# 🎯 雷电模拟器中控系统 - 使用说明

## 📦 打包程序

在VS Code终端中运行：
```cmd
python quick_package.py
```

生成的可执行文件位于：`dist\ins雷电中控.exe`

## 🚀 运行程序

### 开发模式
```cmd
python main.py
```

### 用户模式
直接双击：`dist\ins雷电中控.exe`

## 📋 当前环境

- ✅ 虚拟环境：`venv\` 目录
- ✅ Python版本：3.12.9
- ✅ 已安装依赖：PyQt6, requests, Pillow, psutil, pyinstaller

## 🔧 重新打包

如果需要重新打包，只需运行：
```cmd
python quick_package.py
```

---
**注意**：确保VS Code已选择虚拟环境解释器（底部显示 `Python 3.12.9 (venv)`）
