#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 安装完整依赖包程序
========================================
功能描述: 在虚拟环境中安装requirements.txt中的所有包
使用方法: python 安装完整依赖.py
注意事项: 确保在虚拟环境中运行
========================================
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 安装完整依赖包程序")
    print("=" * 60)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"   Python路径: {sys.executable}")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("   ✅ 在虚拟环境中")
    else:
        print("   ⚠️  不在虚拟环境中（建议使用虚拟环境）")
    
    return True

def install_requirements():
    """安装requirements.txt中的所有包"""
    print("\n📥 安装requirements.txt中的所有包...")
    
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    
    if not requirements_file.exists():
        print("   ❌ 未找到requirements.txt文件")
        return False
    
    try:
        print("   📋 开始安装所有依赖包...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", str(requirements_file),
            "--upgrade"  # 升级到最新版本
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ 所有依赖包安装成功")
            return True
        else:
            print("   ❌ 依赖包安装失败")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 安装过程出错: {e}")
        return False

def install_additional_crypto():
    """安装额外的加密模块"""
    print("\n🔐 安装额外的加密模块...")
    
    try:
        # 安装pycryptodome（解决Crypto模块问题）
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "pycryptodome"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ pycryptodome安装成功")
        else:
            print("   ⚠️  pycryptodome安装失败，但继续...")
        
        return True
        
    except Exception as e:
        print(f"   ⚠️  安装加密模块时出错: {e}")
        return True  # 不阻止继续执行

def verify_installation():
    """验证关键包安装"""
    print("\n🔍 验证关键包安装...")
    
    key_packages = [
        ("PyQt6", "PyQt6图形界面框架"),
        ("requests", "HTTP请求库"),
        ("PIL", "图像处理库（Pillow）"),
        ("psutil", "系统监控库"),
        ("Crypto", "加密库"),
        ("wmi", "Windows管理库"),
        ("PyInstaller", "打包工具")
    ]
    
    working_count = 0
    
    for package, description in key_packages:
        try:
            if package == "PyInstaller":
                __import__("PyInstaller")
            else:
                __import__(package)
            print(f"   ✅ {description}")
            working_count += 1
        except ImportError:
            print(f"   ❌ {description} - 未安装或导入失败")
    
    print(f"\n📊 验证结果: {working_count}/{len(key_packages)} 个关键包可用")
    
    if working_count >= len(key_packages) - 1:  # 允许1个包失败
        print("✅ 依赖验证通过，可以进行打包")
        return True
    else:
        print("⚠️  部分关键包缺失，可能影响程序运行")
        return False

def show_next_steps():
    """显示下一步操作"""
    print("\n" + "=" * 60)
    print("🎉 依赖安装完成！")
    print("=" * 60)
    
    print("\n📋 下一步操作:")
    print("1. 运行打包程序:")
    print("   python 纯净虚拟环境打包.py")
    print("")
    print("2. 或者运行完整打包程序:")
    print("   python 完整打包程序.py")
    print("")
    print("3. 测试程序运行:")
    print("   python main.py")

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查环境
        if not check_environment():
            return False
        
        # 安装requirements.txt中的包
        if not install_requirements():
            print("\n💥 依赖安装失败")
            return False
        
        # 安装额外的加密模块
        install_additional_crypto()
        
        # 验证安装
        verify_installation()
        
        # 显示下一步操作
        show_next_steps()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 安装失败")
    
    input("\n按Enter键退出...")
