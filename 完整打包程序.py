#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 完整打包程序 - 自动安装依赖并打包
========================================
功能描述: 自动安装所有必要依赖，然后进行打包
使用方法: python 完整打包程序.py
注意事项: 会自动安装缺失的模块
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 完整打包程序 - 自动安装依赖并打包")
    print("=" * 60)

def install_required_packages():
    """安装必要的包"""
    print("\n📦 安装必要的包...")
    
    required_packages = [
        "PyQt6",
        "requests", 
        "Pillow",
        "psutil",
        "pycryptodome",  # 加密模块
        "wmi",           # Windows管理模块
        "pyinstaller"    # 打包工具
    ]
    
    for package in required_packages:
        print(f"   📥 检查并安装 {package}...")
        try:
            # 先尝试导入
            if package == "pycryptodome":
                __import__("Crypto")
            elif package == "Pillow":
                __import__("PIL")
            else:
                __import__(package)
            print(f"   ✅ {package} 已安装")
        except ImportError:
            # 如果导入失败，则安装
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True, text=True)
                print(f"   ✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ {package} 安装失败: {e}")
                return False
    
    return True

def clear_build():
    """清理构建文件"""
    print("\n🗑️  清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")
                return False
    return True

def create_complete_package():
    """创建完整打包"""
    print("\n🔨 开始完整打包...")
    
    project_root = Path(__file__).parent
    
    # 完整的打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 包含所有必要模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",
        "--hidden-import=Crypto",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "--hidden-import=Crypto.Hash",
        "--hidden-import=Crypto.Signature",
        "--hidden-import=PIL",
        "--hidden-import=sqlite3",
        "main.py"
    ]
    
    print("📋 完整打包命令:")
    print("   " + " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 完整打包成功")
            return True
        else:
            print("❌ 完整打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        print("   💡 包含所有必要依赖，应该可以正常运行")
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 安装必要包
        if not install_required_packages():
            print("\n💥 依赖安装失败")
            return False
        
        # 清理构建文件
        if not clear_build():
            return False
        
        # 完整打包
        if not create_complete_package():
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 完整打包程序执行完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 包含所有依赖，应该可以正常运行")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
