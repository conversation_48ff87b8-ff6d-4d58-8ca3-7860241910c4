#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 强制虚拟环境打包程序
========================================
功能描述: 强制使用虚拟环境中的Python和PyInstaller进行打包
使用方法: python 强制虚拟环境打包.py
注意事项: 会明确使用venv中的可执行文件
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 强制虚拟环境打包程序")
    print("=" * 60)

def check_venv():
    """检查并获取虚拟环境路径"""
    print("🔍 检查虚拟环境...")
    
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    if not venv_path.exists():
        print("   ❌ 未找到venv目录")
        return None
    
    # Windows路径
    venv_python = venv_path / "Scripts" / "python.exe"
    venv_pyinstaller = venv_path / "Scripts" / "pyinstaller.exe"
    
    if not venv_python.exists():
        print(f"   ❌ 未找到虚拟环境Python: {venv_python}")
        return None
    
    if not venv_pyinstaller.exists():
        print(f"   ❌ 未找到虚拟环境PyInstaller: {venv_pyinstaller}")
        return None
    
    print(f"   ✅ 虚拟环境Python: {venv_python}")
    print(f"   ✅ 虚拟环境PyInstaller: {venv_pyinstaller}")
    
    return {
        'python': str(venv_python),
        'pyinstaller': str(venv_pyinstaller),
        'venv_path': str(venv_path)
    }

def check_venv_packages(venv_info):
    """检查虚拟环境中的包"""
    print("\n📦 检查虚拟环境包...")
    
    try:
        result = subprocess.run([
            venv_info['python'], "-m", "pip", "list", "--format=freeze"
        ], capture_output=True, text=True, check=True)
        
        packages = result.stdout.strip().split('\n')
        print(f"   📊 虚拟环境中共有 {len(packages)} 个包")
        
        # 检查关键包
        key_packages = ['PyQt6', 'pyinstaller', 'requests', 'psutil', 'pycryptodome']
        for pkg in key_packages:
            found = any(line.lower().startswith(pkg.lower()) for line in packages)
            status = "✅" if found else "❌"
            print(f"   {status} {pkg}")
        
        return True
    except Exception as e:
        print(f"   ❌ 检查包列表失败: {e}")
        return False

def clear_build():
    """清理构建文件"""
    print("\n🗑️  清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")
                return False
    return True

def create_package_with_venv(venv_info):
    """使用虚拟环境创建打包"""
    print("\n🔨 使用虚拟环境打包...")
    
    project_root = Path(__file__).parent
    
    # 使用虚拟环境中的pyinstaller
    cmd = [
        venv_info['pyinstaller'],       # 使用虚拟环境的pyinstaller
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 只包含核心必需模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=Crypto",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "main.py"
    ]
    
    print("📋 使用虚拟环境打包命令:")
    print(f"   PyInstaller路径: {venv_info['pyinstaller']}")
    print("   " + " ".join(cmd[1:]))  # 不显示完整路径，太长了
    
    try:
        # 设置环境变量，确保使用虚拟环境
        env = os.environ.copy()
        env['PATH'] = str(Path(venv_info['venv_path']) / 'Scripts') + os.pathsep + env['PATH']
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            print("✅ 虚拟环境打包完成")
            return True
        else:
            print("❌ 虚拟环境打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            if result.stdout:
                print(f"输出信息: {result.stdout[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        # 大小分析
        if size_mb < 40:
            print("   🎉 文件大小很好！（虚拟环境打包成功）")
        elif size_mb < 60:
            print("   ✅ 文件大小可接受")
        else:
            print("   ⚠️  文件仍然较大，可能包含了系统环境的包")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查虚拟环境
        venv_info = check_venv()
        if not venv_info:
            print("\n💥 虚拟环境检查失败")
            return False
        
        # 检查虚拟环境包
        if not check_venv_packages(venv_info):
            print("\n💥 虚拟环境包检查失败")
            return False
        
        # 清理构建文件
        if not clear_build():
            return False
        
        # 使用虚拟环境打包
        if not create_package_with_venv(venv_info):
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 强制虚拟环境打包完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 这次确保使用了虚拟环境中的所有工具")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
