#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 彻底清理重新打包脚本
========================================
功能描述: 清理所有PyInstaller缓存，重新安装依赖，重新打包
使用方法: python 彻底清理重新打包.py
注意事项: 会清理所有缓存，确保使用最新的依赖
========================================
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🧹 彻底清理重新打包 - 雷电模拟器中控系统")
    print("=" * 60)

def clear_pyinstaller_cache():
    """清理PyInstaller缓存"""
    print("\n🧹 清理PyInstaller缓存...")
    
    # 清理临时目录中的PyInstaller缓存
    temp_dir = Path(tempfile.gettempdir())
    pyinstaller_dirs = [
        temp_dir / "_MEI*",
        temp_dir / "pyinstaller*",
        Path.home() / ".pyinstaller",
        Path.home() / "AppData" / "Local" / "pyinstaller"
    ]
    
    for cache_pattern in pyinstaller_dirs:
        try:
            if "*" in str(cache_pattern):
                # 处理通配符
                parent = cache_pattern.parent
                pattern = cache_pattern.name
                if parent.exists():
                    for item in parent.glob(pattern):
                        if item.is_dir():
                            shutil.rmtree(item, ignore_errors=True)
                            print(f"   🗑️  已清理: {item}")
            else:
                if cache_pattern.exists():
                    if cache_pattern.is_dir():
                        shutil.rmtree(cache_pattern, ignore_errors=True)
                    else:
                        cache_pattern.unlink()
                    print(f"   🗑️  已清理: {cache_pattern}")
        except Exception as e:
            print(f"   ⚠️  清理 {cache_pattern} 时出错: {e}")

def clear_project_cache():
    """清理项目缓存"""
    print("\n🗑️  清理项目构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
        project_root / "__pycache__",
        project_root / "*.spec"
    ]
    
    for target in clean_targets:
        try:
            if "*" in str(target):
                # 处理通配符
                parent = target.parent
                pattern = target.name
                for item in parent.glob(pattern):
                    if item.is_file():
                        item.unlink()
                        print(f"   🗑️  已删除: {item.name}")
            else:
                if target.exists():
                    if target.is_dir():
                        shutil.rmtree(target)
                        print(f"   🗑️  已删除: {target.name}")
                    else:
                        target.unlink()
                        print(f"   🗑️  已删除: {target.name}")
        except Exception as e:
            print(f"   ⚠️  删除 {target} 时出错: {e}")

def reinstall_wmi():
    """重新安装WMI模块"""
    print("\n📦 重新安装WMI模块...")
    
    try:
        # 先卸载
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "wmi", "-y"], 
                      capture_output=True, text=True)
        print("   🗑️  已卸载旧的WMI模块")
        
        # 重新安装
        result = subprocess.run([sys.executable, "-m", "pip", "install", "wmi"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ WMI模块重新安装成功")
            return True
        else:
            print(f"   ❌ WMI模块安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ 安装WMI模块时出错: {e}")
        return False

def test_wmi_import():
    """测试WMI模块导入"""
    print("\n🧪 测试WMI模块...")
    
    try:
        subprocess.run([sys.executable, "-c", "import wmi; print('WMI模块测试成功')"], 
                      check=True, capture_output=True, text=True)
        print("   ✅ WMI模块测试通过")
        return True
    except subprocess.CalledProcessError:
        print("   ❌ WMI模块测试失败")
        return False

def package_app():
    """重新打包应用"""
    print("\n🔨 重新打包应用...")
    
    try:
        result = subprocess.run([sys.executable, "quick_package.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ 打包成功")
            return True
        else:
            print("   ❌ 打包失败")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 步骤1: 清理PyInstaller缓存
        clear_pyinstaller_cache()
        
        # 步骤2: 清理项目缓存
        clear_project_cache()
        
        # 步骤3: 重新安装WMI模块
        if not reinstall_wmi():
            print("\n⚠️  WMI模块安装失败，但继续打包...")
        
        # 步骤4: 测试WMI模块
        test_wmi_import()
        
        # 步骤5: 重新打包
        if package_app():
            print("\n🎉 彻底清理重新打包完成！")
            print("📦 输出文件: dist/ins雷电中控.exe")
        else:
            print("\n💥 打包失败")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按Enter键退出...")
