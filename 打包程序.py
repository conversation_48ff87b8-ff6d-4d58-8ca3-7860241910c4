#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 雷电模拟器中控系统 - 主打包程序
========================================
功能描述: 彻底清理缓存并重新打包，解决所有依赖问题
使用方法: python 打包程序.py
注意事项: 确保在虚拟环境中运行
========================================
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 雷电模拟器中控系统 - 主打包程序")
    print("=" * 60)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"   Python路径: {sys.executable}")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    if in_venv:
        print("   ✅ 在虚拟环境中")
    else:
        print("   ⚠️  不在虚拟环境中（建议使用虚拟环境）")

def clear_all_cache():
    """清理所有缓存"""
    print("\n🧹 清理所有缓存...")
    
    project_root = Path(__file__).parent
    
    # 清理项目缓存
    clean_targets = [
        project_root / "build",
        project_root / "dist",
        project_root / "__pycache__",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                if target.is_dir():
                    shutil.rmtree(target)
                    print(f"   🗑️  已删除: {target.name}")
                else:
                    target.unlink()
                    print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除 {target.name} 失败: {e}")
    
    # 清理spec文件
    for spec_file in project_root.glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"   🗑️  已删除: {spec_file.name}")
        except:
            pass

def install_dependencies():
    """安装必要依赖"""
    print("\n📦 检查并安装依赖...")
    
    dependencies = ["wmi", "pyinstaller"]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep} 已安装")
        except ImportError:
            print(f"   📥 正在安装 {dep}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True, text=True)
                print(f"   ✅ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ {dep} 安装失败: {e}")
                return False
    
    return True

def create_package():
    """创建打包"""
    print("\n🔨 开始打包...")
    
    project_root = Path(__file__).parent
    
    # 强化的打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",          # WMI模块
        "--hidden-import=sqlite3",      # 数据库模块
        "--hidden-import=asyncio",      # 异步模块
        "main.py"
    ]
    
    print("📋 执行命令:")
    print("   " + " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包完成")
            return True
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查环境
        check_environment()
        
        # 清理缓存
        clear_all_cache()
        
        # 安装依赖
        if not install_dependencies():
            return False
        
        # 执行打包
        if not create_package():
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 打包成功完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 可以直接运行或分发给用户")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
