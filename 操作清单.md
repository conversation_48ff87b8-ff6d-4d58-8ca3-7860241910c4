# 🎯 雷电模拟器中控系统 - 操作清单

## 📋 当前状态
- ✅ 虚拟环境已创建 (`venv/` 目录)
- ✅ 核心依赖包已安装 (requests, Pillow, psutil, pyinstaller)
- ⚠️ PyQt6 需要手动安装
- ✅ 打包脚本已准备就绪

## 🚀 立即需要执行的操作

### 操作1：激活虚拟环境并安装PyQt6

1. **打开命令提示符**
   - 按 `Win + R`，输入 `cmd`，按回车
   - 或者在开始菜单搜索"命令提示符"

2. **进入项目目录**
   ```cmd
   cd E:\VScodexiangmu\xunileidian2
   ```

3. **激活虚拟环境**
   ```cmd
   venv\Scripts\activate.bat
   ```
   
   成功后命令提示符前会显示 `(venv)`

4. **安装PyQt6**
   ```cmd
   pip install PyQt6
   ```

### 操作2：测试程序运行

在激活的虚拟环境中：

```cmd
python main.py
```

**预期结果**：程序界面正常启动

### 操作3：打包程序

如果程序运行正常：

```cmd
python quick_package.py
```

**预期结果**：在 `dist/` 目录生成 `ins雷电中控.exe`

## 🔧 故障排除

### 问题1：PyQt6安装失败
**解决方案**：
```cmd
# 使用清华镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt6
```

### 问题2：程序运行报错
**检查项**：
- 确保虚拟环境已激活（命令提示符前有 `(venv)`）
- 确保所有依赖包都已安装
- 检查 `app_config.json` 文件是否存在

### 问题3：打包失败
**解决方案**：
```cmd
# 重新安装pyinstaller
pip uninstall pyinstaller
pip install pyinstaller
```

## 📊 验证清单

完成每个操作后，请验证：

- [ ] 虚拟环境激活成功（显示 `(venv)`）
- [ ] PyQt6安装成功（无错误信息）
- [ ] 程序运行正常（界面启动）
- [ ] 打包成功（生成exe文件）
- [ ] exe文件可以独立运行

## 🎯 成功标志

全部完成后，您应该有：

1. **运行环境**：激活的虚拟环境
2. **源程序**：可以通过 `python main.py` 运行
3. **打包程序**：`dist/ins雷电中控.exe` 可执行文件
4. **分发能力**：exe文件可以分发给其他用户

## 📞 如需帮助

如果遇到任何问题，请：

1. 复制完整的错误信息
2. 说明在哪个步骤遇到问题
3. 提供当前的操作环境信息

---

**下一步**：请按照操作1开始执行，完成后告诉我结果！
