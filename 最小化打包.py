#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 最小化打包程序 - 只包含真正必需的依赖
========================================
功能描述: 分析代码实际使用的包，只安装和打包必需的依赖
使用方法: python 最小化打包.py
注意事项: 追求最小文件大小，只包含核心功能
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🎯 最小化打包程序 - 追求最小文件大小")
    print("=" * 60)
    print("💡 只安装和打包真正必需的依赖")
    print()

def get_venv_info():
    """获取虚拟环境信息"""
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    if not venv_path.exists():
        print("❌ 未找到venv目录")
        return None
    
    venv_python = venv_path / "Scripts" / "python.exe"
    if not venv_python.exists():
        print(f"❌ 虚拟环境Python不存在")
        return None
    
    return {
        'python': str(venv_python),
        'venv_path': str(venv_path),
        'scripts_dir': str(venv_path / 'Scripts')
    }

def install_minimal_packages(venv_info):
    """安装最小必需包"""
    print("📦 安装最小必需包...")
    
    # 基于代码分析的真正必需包
    essential_packages = [
        "PyQt6",           # GUI框架 (main.py中使用)
        "requests",        # HTTP请求 (core/authsdk.py中使用)
        "pycryptodome",    # 加密功能 (core/authsdk.py中使用Crypto)
        "psutil",          # 系统监控 (多个文件使用)
        "wmi",             # Windows管理 (core/authsdk.py中使用)
        "pyinstaller"      # 打包工具
    ]
    
    # 可选包（如果程序确实需要才安装）
    optional_packages = [
        "Pillow"           # 图像处理（如果有截图功能）
    ]
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print("   📥 安装核心必需包...")
        for package in essential_packages:
            print(f"      正在安装: {package}")
            cmd = [venv_info['python'], "-m", "pip", "install", package, "--upgrade"]
            result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"      ✅ {package}")
            else:
                print(f"      ❌ {package} 安装失败")
                return False
        
        print("   📥 安装可选包...")
        for package in optional_packages:
            print(f"      正在安装: {package}")
            cmd = [venv_info['python'], "-m", "pip", "install", package, "--upgrade"]
            result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"      ✅ {package}")
            else:
                print(f"      ⚠️  {package} 安装失败（可选包）")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 安装异常: {e}")
        return False

def verify_minimal_packages(venv_info):
    """验证最小包安装"""
    print("\n🔍 验证最小包安装...")
    
    core_packages = [
        ('PyQt6', 'PyQt6.QtWidgets', 'GUI框架'),
        ('requests', 'requests', 'HTTP请求'),
        ('pycryptodome', 'Crypto.Cipher', '加密功能'),
        ('psutil', 'psutil', '系统监控'),
        ('wmi', 'wmi', 'Windows管理'),
        ('pyinstaller', 'PyInstaller', '打包工具')
    ]
    
    working_count = 0
    
    for pkg_name, import_name, description in core_packages:
        try:
            test_cmd = [venv_info['python'], "-c", f"import {import_name}; print('OK')"]
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"   ✅ {pkg_name:<15} - {description}")
                working_count += 1
            else:
                print(f"   ❌ {pkg_name:<15} - {description}")
        except:
            print(f"   ❌ {pkg_name:<15} - {description}")
    
    success_rate = working_count / len(core_packages)
    print(f"\n   📊 核心包验证: {working_count}/{len(core_packages)} ({success_rate:.1%})")
    
    if success_rate < 1.0:  # 要求100%核心包成功
        print("   ❌ 核心包验证失败，无法打包")
        return False
    
    print("   🎉 核心包验证通过")
    return True

def clear_build():
    """清理构建文件"""
    print("\n🗑️  清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [project_root / "build", project_root / "dist"]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")

def minimal_package(venv_info):
    """最小化打包"""
    print("\n🔨 最小化打包...")
    
    project_root = Path(__file__).parent
    
    # 最小化打包命令 - 只包含确实需要的隐藏导入
    cmd = [
        venv_info['python'], "-m", "PyInstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 只包含真正必需的隐藏导入
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=wmi",
        "--hidden-import=Crypto.Cipher",
        "--hidden-import=Crypto.PublicKey",
        "--hidden-import=Crypto.Hash",
        # 不包含大型可选包
        # "--hidden-import=cv2",        # OpenCV很大，不包含
        # "--hidden-import=numpy",      # NumPy很大，不包含
        # "--hidden-import=aiohttp",    # 异步HTTP，不包含
        "main.py"
    ]
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PATH'] = venv_info['scripts_dir'] + os.pathsep + env.get('PATH', '')
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print("   📋 开始最小化打包...")
        print("   ⏳ 预计时间: 2-3分钟...")
        
        result = subprocess.run(cmd, cwd=project_root, env=env, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("   ✅ 最小化打包成功")
            return True
        else:
            print("   ❌ 最小化打包失败")
            if result.stderr:
                print(f"   错误: {result.stderr[:300]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 打包超时")
        return False
    except Exception as e:
        print(f"   ❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证最小化打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        # 大小评估
        if size_mb < 40:
            print("   🎉 文件大小优秀！最小化打包成功")
        elif size_mb < 60:
            print("   ✅ 文件大小良好")
        elif size_mb < 80:
            print("   ⚠️  文件大小一般，可能包含了额外依赖")
        else:
            print("   ❌ 文件仍然很大，最小化失败")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 获取虚拟环境信息
        venv_info = get_venv_info()
        if not venv_info:
            return False
        
        # 安装最小必需包
        if not install_minimal_packages(venv_info):
            print("\n💥 最小包安装失败")
            return False
        
        # 验证最小包
        if not verify_minimal_packages(venv_info):
            print("\n💥 最小包验证失败")
            return False
        
        # 清理构建文件
        clear_build()
        
        # 最小化打包
        if not minimal_package(venv_info):
            print("\n💥 最小化打包失败")
            return False
        
        # 验证结果
        if not verify_result():
            print("\n💥 结果验证失败")
            return False
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 最小化打包成功！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 文件大小已最小化，只包含必需功能")
    else:
        print("💥 最小化打包失败")
    print("=" * 60)
    
    input("\n按Enter键退出...")
