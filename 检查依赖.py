#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 检查依赖包安装状态
========================================
功能描述: 检查当前环境中所有必要依赖的安装状态
使用方法: python 检查依赖.py
注意事项: 会显示每个包的安装状态和版本
========================================
"""

import sys

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔍 检查依赖包安装状态")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    print(f"虚拟环境: {'✅ 是' if in_venv else '❌ 否'}")
    print()

def check_package(package_name, import_name=None, description=""):
    """检查单个包的安装状态"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', '未知版本')
        print(f"✅ {package_name:<20} {version:<15} {description}")
        return True
    except ImportError:
        print(f"❌ {package_name:<20} {'未安装':<15} {description}")
        return False

def check_special_packages():
    """检查特殊包"""
    print("🔍 检查特殊包...")
    
    # 检查Pillow (PIL)
    try:
        import PIL
        print(f"✅ {'Pillow':<20} {PIL.__version__:<15} 图像处理库")
        pillow_ok = True
    except ImportError:
        print(f"❌ {'Pillow':<20} {'未安装':<15} 图像处理库")
        pillow_ok = False
    
    # 检查Crypto (pycryptodome)
    try:
        import Crypto
        from Crypto.Cipher import PKCS1_v1_5
        print(f"✅ {'pycryptodome':<20} {Crypto.__version__:<15} 加密库")
        crypto_ok = True
    except ImportError:
        print(f"❌ {'pycryptodome':<20} {'未安装':<15} 加密库")
        crypto_ok = False
    
    # 检查sqlite3 (标准库)
    try:
        import sqlite3
        print(f"✅ {'sqlite3':<20} {sqlite3.sqlite_version:<15} 数据库 (标准库)")
        sqlite_ok = True
    except ImportError:
        print(f"❌ {'sqlite3':<20} {'未安装':<15} 数据库 (标准库)")
        sqlite_ok = False
    
    return pillow_ok, crypto_ok, sqlite_ok

def main():
    """主函数"""
    print_header()
    
    # 基础包检查
    print("🔍 检查基础包...")
    packages = [
        ("PyQt6", "PyQt6", "GUI框架"),
        ("requests", "requests", "HTTP请求库"),
        ("psutil", "psutil", "系统监控库"),
        ("wmi", "wmi", "Windows管理库"),
        ("pyinstaller", "PyInstaller", "打包工具")
    ]
    
    installed_count = 0
    total_count = len(packages)
    
    for package_name, import_name, description in packages:
        if check_package(package_name, import_name, description):
            installed_count += 1
    
    print()
    
    # 特殊包检查
    pillow_ok, crypto_ok, sqlite_ok = check_special_packages()
    
    if pillow_ok:
        installed_count += 1
    if crypto_ok:
        installed_count += 1
    if sqlite_ok:
        installed_count += 1
    
    total_count += 3
    
    print()
    print("=" * 60)
    print(f"📊 检查结果: {installed_count}/{total_count} 个包已安装")
    
    if installed_count == total_count:
        print("🎉 所有依赖包都已正确安装！")
        print("💡 可以开始打包程序了")
    else:
        print("⚠️  部分依赖包缺失，需要安装")
        
        missing_packages = []
        if not pillow_ok:
            missing_packages.append("Pillow")
        if not crypto_ok:
            missing_packages.append("pycryptodome")
        
        if missing_packages:
            print("\n📥 需要安装的包:")
            for pkg in missing_packages:
                print(f"   pip install {pkg}")
            
            print(f"\n🚀 或者运行: pip install {' '.join(missing_packages)}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按Enter键退出...")
