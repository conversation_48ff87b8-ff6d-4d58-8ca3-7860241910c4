@echo off
echo 🗑️  强制清理构建文件...

REM 强制结束可能占用文件的进程
echo 正在结束相关进程...
taskkill /f /im "ins雷电中控.exe" >nul 2>&1
taskkill /f /im "python.exe" >nul 2>&1
taskkill /f /im "pythonw.exe" >nul 2>&1

REM 等待进程完全结束
timeout /t 2 >nul

REM 删除构建目录
echo 正在删除构建文件...
if exist "build" (
    rmdir /s /q "build"
    echo    ✅ 已删除 build 目录
)

if exist "dist" (
    rmdir /s /q "dist"
    echo    ✅ 已删除 dist 目录
)

if exist "ins雷电中控.spec" (
    del /q "ins雷电中控.spec"
    echo    ✅ 已删除 spec 文件
)

echo 🎉 清理完成！
echo.
echo 现在可以重新打包了：
echo venv\Scripts\python.exe quick_package.py
echo.
pause
