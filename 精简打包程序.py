#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 精简打包程序 - 只包含必要模块
========================================
功能描述: 只包含程序运行必需的模块，减小文件大小
使用方法: python 精简打包程序.py
注意事项: 可能需要根据实际运行情况调整
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 精简打包程序 - 只包含必要模块")
    print("=" * 60)

def clear_build():
    """清理构建文件"""
    print("\n🗑️  清理构建文件...")
    
    project_root = Path(__file__).parent
    clean_targets = [
        project_root / "build",
        project_root / "dist",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                shutil.rmtree(target)
                print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除失败: {e}")
                return False
    return True

def create_minimal_package():
    """创建精简打包"""
    print("\n🔨 开始精简打包...")
    
    project_root = Path(__file__).parent
    
    # 精简的打包命令 - 只包含核心必需模块
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 只包含绝对必需的模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        # 暂时不包含wmi，看看是否真的必需
        "main.py"
    ]
    
    print("📋 精简打包命令:")
    print("   " + " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 精简打包完成")
            return True
        else:
            print("❌ 精简打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        # 大小对比
        if size_mb < 50:
            print("   🎉 文件大小理想！")
        elif size_mb < 70:
            print("   ✅ 文件大小可接受")
        else:
            print("   ⚠️  文件较大，可能包含了不必要的模块")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        print("💡 这个版本会创建更小的exe文件")
        print("💡 如果运行时缺少模块，我们再添加")
        
        # 清理构建文件
        if not clear_build():
            return False
        
        # 执行精简打包
        if not create_minimal_package():
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 精简打包完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 请测试运行，如果缺少模块我们再添加")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
