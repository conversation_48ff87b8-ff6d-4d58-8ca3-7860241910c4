#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 纯净虚拟环境打包程序
========================================
功能描述: 彻底隔离系统环境，只使用虚拟环境进行打包
使用方法: python 纯净虚拟环境打包.py
注意事项: 会重新安装虚拟环境中的PyInstaller
========================================
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 纯净虚拟环境打包程序")
    print("=" * 60)

def check_and_setup_venv():
    """检查并设置纯净虚拟环境"""
    print("🔍 检查虚拟环境...")
    
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    if not venv_path.exists():
        print("   ❌ 未找到venv目录")
        return None
    
    # Windows路径
    venv_python = venv_path / "Scripts" / "python.exe"
    venv_pip = venv_path / "Scripts" / "pip.exe"
    
    if not venv_python.exists():
        print(f"   ❌ 未找到虚拟环境Python: {venv_python}")
        return None
    
    print(f"   ✅ 虚拟环境Python: {venv_python}")
    
    return {
        'python': str(venv_python),
        'pip': str(venv_pip),
        'scripts_dir': str(venv_path / "Scripts"),
        'venv_path': str(venv_path)
    }

def reinstall_pyinstaller(venv_info):
    """重新安装虚拟环境中的PyInstaller"""
    print("\n🔄 重新安装虚拟环境中的PyInstaller...")
    
    try:
        # 先卸载PyInstaller
        print("   🗑️  卸载旧的PyInstaller...")
        subprocess.run([
            venv_info['python'], "-m", "pip", "uninstall", "pyinstaller", "-y"
        ], capture_output=True, text=True)
        
        # 重新安装PyInstaller
        print("   📥 重新安装PyInstaller...")
        result = subprocess.run([
            venv_info['python'], "-m", "pip", "install", "pyinstaller"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ PyInstaller重新安装成功")
            
            # 验证PyInstaller路径
            pyinstaller_path = Path(venv_info['scripts_dir']) / "pyinstaller.exe"
            if pyinstaller_path.exists():
                print(f"   ✅ PyInstaller路径: {pyinstaller_path}")
                return str(pyinstaller_path)
            else:
                print("   ❌ PyInstaller安装后未找到可执行文件")
                return None
        else:
            print(f"   ❌ PyInstaller安装失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"   ❌ 重新安装PyInstaller时出错: {e}")
        return None

def install_minimal_deps(venv_info):
    """安装最小必要依赖"""
    print("\n📦 安装最小必要依赖...")
    
    # 只安装绝对必要的包
    minimal_deps = [
        "PyQt6",
        "requests", 
        "psutil",
        "pycryptodome"
    ]
    
    for dep in minimal_deps:
        print(f"   📥 安装 {dep}...")
        try:
            result = subprocess.run([
                venv_info['python'], "-m", "pip", "install", dep
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"   ✅ {dep} 安装成功")
            else:
                print(f"   ❌ {dep} 安装失败")
                return False
        except Exception as e:
            print(f"   ❌ 安装 {dep} 时出错: {e}")
            return False
    
    return True

def clear_all_cache():
    """清理所有缓存"""
    print("\n🧹 清理所有缓存...")
    
    project_root = Path(__file__).parent
    
    # 清理项目缓存
    clean_targets = [
        project_root / "build",
        project_root / "dist",
        project_root / "__pycache__",
    ]
    
    for target in clean_targets:
        if target.exists():
            try:
                if target.is_dir():
                    shutil.rmtree(target)
                    print(f"   🗑️  已删除: {target.name}")
                else:
                    target.unlink()
                    print(f"   🗑️  已删除: {target.name}")
            except Exception as e:
                print(f"   ⚠️  删除 {target.name} 失败: {e}")
    
    # 清理spec文件
    for spec_file in project_root.glob("*.spec"):
        try:
            spec_file.unlink()
            print(f"   🗑️  已删除: {spec_file.name}")
        except:
            pass

def create_isolated_package(venv_info, pyinstaller_path):
    """创建隔离环境打包"""
    print("\n🔨 创建隔离环境打包...")
    
    project_root = Path(__file__).parent
    
    # 使用绝对路径的PyInstaller
    cmd = [
        pyinstaller_path,               # 使用虚拟环境的pyinstaller绝对路径
        "--onefile",                    # 单文件
        "--windowed",                   # 隐藏控制台
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不询问覆盖
        "--name=ins雷电中控",           # 程序名
        f"--add-data={project_root / 'img'};img",           # 图片资源
        f"--add-data={project_root / 'app_config.json'};.", # 配置文件
        # 最小必要模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=Crypto.Cipher",
        "main.py"
    ]
    
    print("📋 隔离环境打包命令:")
    print(f"   PyInstaller: {pyinstaller_path}")
    print("   " + " ".join(cmd[1:]))
    
    try:
        # 设置纯净环境变量
        env = os.environ.copy()
        
        # 只保留虚拟环境的路径，移除系统Python路径
        venv_scripts = venv_info['scripts_dir']
        env['PATH'] = venv_scripts + os.pathsep + env.get('SYSTEMROOT', '') + r'\System32'
        
        # 清除可能影响的Python环境变量
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print(f"   🔒 使用隔离环境变量，PATH优先级: {venv_scripts}")
        
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            print("✅ 隔离环境打包成功")
            return True
        else:
            print("❌ 隔离环境打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def verify_result():
    """验证结果"""
    print("\n🔍 验证打包结果...")
    
    project_root = Path(__file__).parent
    exe_file = project_root / "dist" / "ins雷电中控.exe"
    
    if exe_file.exists():
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"   ✅ 可执行文件: {exe_file}")
        print(f"   📏 文件大小: {size_mb:.1f} MB")
        
        # 大小分析
        if size_mb < 50:
            print("   🎉 文件大小理想！（纯净虚拟环境打包成功）")
        elif size_mb < 70:
            print("   ✅ 文件大小可接受（虚拟环境打包）")
        else:
            print("   ⚠️  文件仍然较大，可能仍有系统环境影响")
        
        return True
    else:
        print("   ❌ 未找到可执行文件")
        return False

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查虚拟环境
        venv_info = check_and_setup_venv()
        if not venv_info:
            return False
        
        # 重新安装PyInstaller
        pyinstaller_path = reinstall_pyinstaller(venv_info)
        if not pyinstaller_path:
            return False
        
        # 安装最小依赖
        if not install_minimal_deps(venv_info):
            return False
        
        # 清理所有缓存
        clear_all_cache()
        
        # 创建隔离环境打包
        if not create_isolated_package(venv_info, pyinstaller_path):
            return False
        
        # 验证结果
        if not verify_result():
            return False
        
        print("\n🎉 纯净虚拟环境打包完成！")
        print("📦 输出文件: dist/ins雷电中控.exe")
        print("💡 这次确保完全隔离了系统环境")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 打包失败")
    
    input("\n按Enter键退出...")
