# 🎯 雷电模拟器中控系统 - 虚拟环境和打包说明

## 📋 概述

本文档说明如何为雷电模拟器中控系统创建虚拟环境并打包程序。

## 🔧 已创建的文件

### 1. 依赖文件
- `requirements.txt` - Python包依赖列表
- `setup_venv_simple.py` - 简化虚拟环境设置脚本
- `package_with_venv.py` - 虚拟环境打包脚本
- `quick_package.py` - 快速打包脚本
- `test_venv.py` - 虚拟环境测试脚本

### 2. 激活脚本
- `activate_venv.bat` - Windows虚拟环境激活脚本

### 3. 虚拟环境目录
- `venv/` - 虚拟环境目录（已创建）

## 🚀 使用方法

### 方法一：使用激活脚本（推荐）

1. **激活虚拟环境**
   ```cmd
   双击运行: activate_venv.bat
   ```

2. **在激活的环境中运行程序**
   ```cmd
   python main.py
   ```

3. **在激活的环境中打包程序**
   ```cmd
   python package_with_venv.py
   ```

### 方法二：手动操作

1. **激活虚拟环境**
   ```cmd
   venv\Scripts\activate.bat
   ```

2. **安装缺失的包（如果需要）**
   ```cmd
   pip install PyQt6
   pip install requests
   pip install Pillow
   pip install psutil
   pip install pyinstaller
   ```

3. **运行程序**
   ```cmd
   python main.py
   ```

4. **打包程序**
   ```cmd
   python quick_package.py
   ```

### 方法三：使用现有环境打包

如果虚拟环境有问题，可以直接使用系统Python环境：

```cmd
python quick_package.py
```

## 📦 打包结果

成功打包后，会在 `dist/` 目录下生成：
- `ins雷电中控.exe` - 可执行文件

## 🔍 虚拟环境状态

根据设置过程，当前虚拟环境状态：

### ✅ 已成功安装的包
- requests (HTTP请求库)
- Pillow (图像处理库)
- psutil (系统监控库)
- pyinstaller (打包工具)

### ⚠️ 可能需要手动安装的包
- PyQt6 (图形界面框架)

### 📊 安装结果
- 4/5 个核心包安装成功
- 虚拟环境基本可用

## 🛠️ 故障排除

### 1. PyQt6安装失败
```cmd
# 激活虚拟环境后手动安装
venv\Scripts\activate.bat
pip install PyQt6
```

### 2. 网络超时问题
```cmd
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt6
```

### 3. 权限问题
- 以管理员身份运行命令提示符
- 或者使用 `--user` 参数安装

### 4. 打包失败
- 确保所有依赖包都已安装
- 检查 `main.py` 文件是否存在
- 检查 `img/` 目录和 `app_config.json` 文件

## 📝 注意事项

1. **虚拟环境路径**: `E:\VScodexiangmu\xunileidian2\venv`

2. **激活状态检查**: 激活虚拟环境后，命令提示符前会显示 `(venv)`

3. **退出虚拟环境**: 使用 `deactivate` 命令

4. **首次运行**: 打包后的exe文件首次运行可能需要较长时间

5. **分发**: 生成的exe文件可以直接分发给其他用户，无需安装Python环境

## 🎯 快速开始

最简单的使用方法：

1. 双击 `activate_venv.bat`
2. 在打开的命令窗口中输入：`python main.py`
3. 如需打包，输入：`python quick_package.py`

## 📞 技术支持

如果遇到问题：

1. 检查Python版本（需要3.8+）
2. 确保网络连接正常
3. 尝试使用管理员权限
4. 查看错误信息并根据提示操作

---

**创建时间**: 2025-07-30  
**适用版本**: Python 3.8+  
**测试环境**: Windows 10/11
