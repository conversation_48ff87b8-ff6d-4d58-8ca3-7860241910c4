#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 虚拟环境完整安装程序
========================================
功能描述: 确保在虚拟环境中安装requirements.txt的所有依赖
使用方法: python 虚拟环境完整安装.py
注意事项: 会检查环境变量并强制使用虚拟环境
========================================
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("📦 虚拟环境完整安装程序")
    print("=" * 60)

def check_venv_status():
    """详细检查虚拟环境状态"""
    print("🔍 详细检查虚拟环境状态...")
    
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    print(f"   项目根目录: {project_root}")
    print(f"   虚拟环境目录: {venv_path}")
    print(f"   当前Python路径: {sys.executable}")
    
    # 检查虚拟环境目录
    if not venv_path.exists():
        print("   ❌ venv目录不存在")
        return None
    
    # Windows路径
    venv_python = venv_path / "Scripts" / "python.exe"
    venv_pip = venv_path / "Scripts" / "pip.exe"
    
    if not venv_python.exists():
        print(f"   ❌ 虚拟环境Python不存在: {venv_python}")
        return None
    
    # 检查是否在虚拟环境中
    in_venv = (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    print(f"   虚拟环境检测: {'✅ 是' if in_venv else '❌ 否'}")
    
    # 检查Python路径是否指向虚拟环境
    current_python_str = str(sys.executable).lower()
    venv_python_str = str(venv_python).lower()
    
    using_venv_python = venv_python_str in current_python_str
    print(f"   使用虚拟环境Python: {'✅ 是' if using_venv_python else '❌ 否'}")
    
    if not using_venv_python:
        print(f"   ⚠️  当前Python: {sys.executable}")
        print(f"   ⚠️  应该使用: {venv_python}")
    
    return {
        'python': str(venv_python),
        'pip': str(venv_pip),
        'venv_path': str(venv_path),
        'in_venv': in_venv,
        'using_correct_python': using_venv_python
    }

def force_install_with_venv(venv_info):
    """强制使用虚拟环境安装requirements.txt"""
    print("\n📥 强制使用虚拟环境安装requirements.txt...")
    
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    
    if not requirements_file.exists():
        print("   ❌ 未找到requirements.txt文件")
        return False
    
    print(f"   📋 requirements文件: {requirements_file}")
    print(f"   🐍 使用Python: {venv_info['python']}")
    
    try:
        # 设置环境变量，确保使用虚拟环境
        env = os.environ.copy()
        
        # 设置PATH，虚拟环境优先
        venv_scripts = str(Path(venv_info['venv_path']) / 'Scripts')
        env['PATH'] = venv_scripts + os.pathsep + env.get('PATH', '')
        
        # 清除可能干扰的Python环境变量
        env.pop('PYTHONPATH', None)
        env.pop('PYTHONHOME', None)
        
        print("   🔧 设置环境变量...")
        print(f"   📁 PATH优先级: {venv_scripts}")
        
        # 使用虚拟环境的Python直接安装
        cmd = [
            venv_info['python'], 
            "-m", "pip", "install", 
            "-r", str(requirements_file),
            "--upgrade",
            "--no-cache-dir"  # 不使用缓存，确保最新安装
        ]
        
        print("   📋 执行命令:")
        print("   " + " ".join(cmd))
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("   ✅ requirements.txt安装成功")
            return True
        else:
            print("   ❌ requirements.txt安装失败")
            if result.stderr:
                print(f"   错误信息: {result.stderr[:500]}...")
            if result.stdout:
                print(f"   输出信息: {result.stdout[:500]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 安装超时，但可能仍在继续...")
        return False
    except Exception as e:
        print(f"   ❌ 安装异常: {e}")
        return False

def verify_installation(venv_info):
    """验证安装结果"""
    print("\n🔍 验证安装结果...")
    
    # 使用虚拟环境的Python检查包
    try:
        result = subprocess.run([
            venv_info['python'], "-m", "pip", "list", "--format=freeze"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            packages = result.stdout.strip().split('\n')
            print(f"   📊 虚拟环境中共安装了 {len(packages)} 个包")
            
            # 检查关键包
            key_packages = [
                'PyQt6', 'requests', 'Pillow', 'psutil', 'wmi', 
                'pycryptodome', 'pyinstaller', 'opencv-python', 'numpy'
            ]
            
            found_packages = []
            for pkg in key_packages:
                found = any(line.lower().startswith(pkg.lower()) for line in packages)
                status = "✅" if found else "❌"
                print(f"   {status} {pkg}")
                if found:
                    found_packages.append(pkg)
            
            print(f"\n   📈 关键包安装率: {len(found_packages)}/{len(key_packages)}")
            
            return len(found_packages) >= len(key_packages) - 2  # 允许2个包失败
        else:
            print("   ❌ 无法获取包列表")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证异常: {e}")
        return False

def show_next_steps():
    """显示下一步操作"""
    print("\n" + "=" * 60)
    print("🎉 虚拟环境依赖安装完成！")
    print("=" * 60)
    
    print("\n📋 下一步操作:")
    print("1. 确认在虚拟环境中运行打包:")
    print("   python 一键安装并打包.py")
    print("")
    print("2. 或者使用纯净虚拟环境打包:")
    print("   python 纯净虚拟环境打包.py")
    print("")
    print("3. 检查环境变量是否正确:")
    print("   echo $PATH  (Linux/Mac)")
    print("   echo %PATH% (Windows)")

def main():
    """主函数"""
    try:
        print_header()
        
        # 检查虚拟环境状态
        venv_info = check_venv_status()
        if not venv_info:
            print("\n💥 虚拟环境检查失败")
            return False
        
        if not venv_info['using_correct_python']:
            print("\n⚠️  当前不在虚拟环境中，但会强制使用虚拟环境安装")
        
        # 强制使用虚拟环境安装
        if not force_install_with_venv(venv_info):
            print("\n💥 依赖安装失败")
            return False
        
        # 验证安装
        if verify_installation(venv_info):
            print("\n✅ 依赖验证通过")
        else:
            print("\n⚠️  部分依赖可能有问题，但可以尝试打包")
        
        # 显示下一步
        show_next_steps()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return False
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💥 安装失败")
    
    input("\n按Enter键退出...")
